import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import LandingPage from './pages/LandingPage'
import RoadmapGenerator from './pages/RoadmapGenerator'
import RoadmapDisplay from './pages/RoadmapDisplay'
import ModuleLessons from './pages/ModuleLessons'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-900">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/generator" element={<RoadmapGenerator />} />
          <Route path="/roadmap" element={<RoadmapDisplay />} />
          <Route path="/module/:moduleId" element={<ModuleLessons />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
