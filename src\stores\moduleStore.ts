import { create } from 'zustand'
import { aiService } from '../services/aiService'
import { dynamicLessonGenerator, type Lesson as DynamicLesson, type LessonGenerationOptions } from '../services/dynamicLessonGenerator'

export interface LessonContent {
  introduction: string
  mainContent: string
  examples: string[]
  exercises: { question: string; answer: string; type?: string }[]
  vocabulary?: { term: string; reading: string; meaning: string }[]
  grammar?: { pattern: string; explanation: string; examples: string[] }[]
}

export interface Lesson {
  id: string
  title: string
  type: 'reading' | 'practice' | 'quiz' | 'video' | 'conversation'
  duration: string
  completed: boolean
  content: LessonContent
  difficulty: number
  prerequisites: string[]
  aiGenerated: boolean
  generatedAt: Date
}

export interface ModuleProgress {
  moduleId: string
  currentLessonIndex: number
  completedLessons: string[]
  totalTimeSpent: number
  lastAccessedAt: Date
  progressPercentage: number
}

export interface ModuleState {
  id: string
  title: string
  description: string
  lessons: Lesson[]
  isInitialized: boolean
  isGeneratingLessons: boolean
  progress: ModuleProgress
  userLevel: string
  focusAreas: string[]
}

interface ModuleStore {
  modules: { [moduleId: string]: ModuleState }
  currentModuleId: string | null
  isLoading: boolean
  error: string | null
  
  // Actions
  initializeModule: (moduleId: string, userLevel: string, focusAreas: string[]) => Promise<void>
  generateLessons: (moduleId: string, lessonCount: number) => Promise<void>
  updateLessonProgress: (moduleId: string, lessonId: string, completed: boolean) => void
  setCurrentModule: (moduleId: string) => void
  markLessonComplete: (moduleId: string, lessonId: string) => void
  updateModuleProgress: (moduleId: string) => void
  getModuleById: (moduleId: string) => ModuleState | null
  resetModule: (moduleId: string) => void
}

export const useModuleStore = create<ModuleStore>((set, get) => ({
  modules: {},
  currentModuleId: null,
  isLoading: false,
  error: null,

  initializeModule: async (moduleId: string, userLevel: string, focusAreas: string[]) => {
    set({ isLoading: true, error: null })
    
    try {
      const existingModule = get().modules[moduleId]
      
      if (existingModule && existingModule.isInitialized) {
        set({ currentModuleId: moduleId, isLoading: false })
        return
      }

      // Create dynamic module state with user context
      const userContext = { goals: [], focusAreas };
      const moduleState: ModuleState = {
        id: moduleId,
        title: generateModuleTitle(moduleId, userContext),
        description: generateModuleDescription(moduleId, userContext),
        lessons: [],
        isInitialized: false,
        isGeneratingLessons: false,
        progress: {
          moduleId,
          currentLessonIndex: 0,
          completedLessons: [],
          totalTimeSpent: 0,
          lastAccessedAt: new Date(),
          progressPercentage: 0
        },
        userLevel,
        focusAreas
      }

      set(state => ({
        modules: {
          ...state.modules,
          [moduleId]: moduleState
        },
        currentModuleId: moduleId,
        isLoading: false
      }))

      // Generate initial lessons
      await get().generateLessons(moduleId, 5)
      
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to initialize module',
        isLoading: false 
      })
    }
  },

  generateLessons: async (moduleId: string, lessonCount: number) => {
    const module = get().modules[moduleId]
    if (!module) return

    set(state => ({
      modules: {
        ...state.modules,
        [moduleId]: {
          ...module,
          isGeneratingLessons: true
        }
      }
    }))

    try {
      const generatedLessons = await generateDynamicLessons(
        moduleId,
        module.title,
        module.description,
        module.userLevel,
        [], // userGoals - could be passed from user preferences
        module.focusAreas,
        'adaptive', // learningStyle - could be passed from user preferences
        lessonCount,
        module.lessons.length,
        module.lessons
      )

      set(state => ({
        modules: {
          ...state.modules,
          [moduleId]: {
            ...state.modules[moduleId],
            lessons: [...state.modules[moduleId].lessons, ...generatedLessons],
            isInitialized: true,
            isGeneratingLessons: false
          }
        }
      }))

    } catch (error) {
      set(state => ({
        modules: {
          ...state.modules,
          [moduleId]: {
            ...state.modules[moduleId],
            isGeneratingLessons: false
          }
        },
        error: error instanceof Error ? error.message : 'Failed to generate lessons'
      }))
    }
  },

  updateLessonProgress: (moduleId: string, lessonId: string, completed: boolean) => {
    const module = get().modules[moduleId]
    if (!module) return

    const updatedLessons = module.lessons.map(lesson => 
      lesson.id === lessonId ? { ...lesson, completed } : lesson
    )

    const completedLessons = completed 
      ? [...module.progress.completedLessons, lessonId].filter((id, index, arr) => arr.indexOf(id) === index)
      : module.progress.completedLessons.filter(id => id !== lessonId)

    const progressPercentage = (completedLessons.length / updatedLessons.length) * 100

    set(state => ({
      modules: {
        ...state.modules,
        [moduleId]: {
          ...state.modules[moduleId],
          lessons: updatedLessons,
          progress: {
            ...state.modules[moduleId].progress,
            completedLessons,
            progressPercentage,
            lastAccessedAt: new Date()
          }
        }
      }
    }))
  },

  setCurrentModule: (moduleId: string) => {
    set({ currentModuleId: moduleId })
  },

  markLessonComplete: (moduleId: string, lessonId: string) => {
    get().updateLessonProgress(moduleId, lessonId, true)
    get().updateModuleProgress(moduleId)
  },

  updateModuleProgress: (moduleId: string) => {
    const module = get().modules[moduleId]
    if (!module) return

    const currentLessonIndex = module.lessons.findIndex(lesson => !lesson.completed)
    
    set(state => ({
      modules: {
        ...state.modules,
        [moduleId]: {
          ...state.modules[moduleId],
          progress: {
            ...state.modules[moduleId].progress,
            currentLessonIndex: currentLessonIndex === -1 ? module.lessons.length - 1 : currentLessonIndex,
            lastAccessedAt: new Date()
          }
        }
      }
    }))
  },

  getModuleById: (moduleId: string) => {
    return get().modules[moduleId] || null
  },

  resetModule: (moduleId: string) => {
    set(state => {
      const { [moduleId]: removed, ...remainingModules } = state.modules
      return {
        modules: remainingModules,
        currentModuleId: state.currentModuleId === moduleId ? null : state.currentModuleId
      }
    })
  }
}))

// Dynamic helper functions
function generateModuleTitle(moduleId: string, userContext?: any): string {
  // Generate contextual titles based on module ID and user preferences
  const baseTitle = moduleId.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  if (userContext?.goals?.includes('travel')) {
    return `${baseTitle} for Travel`;
  }
  
  if (userContext?.goals?.includes('business')) {
    return `${baseTitle} for Business`;
  }
  
  return baseTitle;
}

function generateModuleDescription(moduleId: string, userContext?: any): string {
  // Generate contextual descriptions based on module content and user goals
  const descriptions: { [key: string]: string } = {
    'hiragana': 'Master the foundational Japanese syllabary system',
    'katakana': 'Learn the syllabary used for foreign words and emphasis',
    'grammar': 'Build essential sentence structures and communication patterns',
    'vocabulary': 'Acquire core words for effective communication',
    'kanji': 'Develop reading and writing skills with Chinese characters',
    'conversation': 'Practice real-world dialogue and speaking skills'
  };
  
  // Find matching description based on module ID keywords
  for (const [key, desc] of Object.entries(descriptions)) {
    if (moduleId.includes(key)) {
      if (userContext?.focusAreas?.includes('speaking')) {
        return `${desc} with emphasis on speaking practice`;
      }
      if (userContext?.focusAreas?.includes('reading')) {
        return `${desc} with focus on reading comprehension`;
      }
      return desc;
    }
  }
  
  return 'Comprehensive Japanese learning module tailored to your goals';
}

async function generateDynamicLessons(
  moduleId: string,
  moduleTitle: string,
  moduleDescription: string,
  userLevel: string,
  userGoals: string[],
  focusAreas: string[],
  learningStyle: string,
  lessonCount: number,
  startIndex: number,
  previousLessons?: Lesson[]
): Promise<Lesson[]> {
  const options: LessonGenerationOptions = {
    moduleId,
    moduleTitle,
    moduleDescription,
    userLevel,
    userGoals,
    focusAreas,
    learningStyle,
    lessonCount,
    startIndex,
    previousLessons
  };

  try {
    const dynamicLessons = await dynamicLessonGenerator.generateLessons(options);
    
    // Convert DynamicLesson to Lesson format
    return dynamicLessons.map(lesson => ({
      id: lesson.id,
      title: lesson.title,
      type: lesson.type,
      duration: lesson.duration,
      completed: lesson.completed,
      content: lesson.content,
      difficulty: lesson.difficulty,
      prerequisites: lesson.prerequisites || [],
      aiGenerated: lesson.aiGenerated,
      generatedAt: lesson.generatedAt
    }));
  } catch (error) {
    console.error('Error generating dynamic lessons:', error);
    throw error; // Let the calling function handle the error
  }
}