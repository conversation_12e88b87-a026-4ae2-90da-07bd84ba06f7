import React, { useState, useEffect, useRef } from 'react'

interface CursorPosition {
  x: number
  y: number
}

interface AnimatedCursorProps {
  position: CursorPosition
  isVisible: boolean
  isClicking: boolean
  onClickComplete?: () => void
}

const AnimatedCursor: React.FC<AnimatedCursorProps> = ({ 
  position, 
  isVisible, 
  isClicking, 
  onClickComplete 
}) => {
  const [clickRipples, setClickRipples] = useState<CursorPosition[]>([])
  const [isHovering, setIsHovering] = useState(false)
  const [currentPosition, setCurrentPosition] = useState<CursorPosition>(position)
  const animationFrameRef = useRef<number | null>(null)

  // Smooth cursor movement with animation frames
  useEffect(() => {
    if (!isVisible) return
    
    const animateCursor = () => {
      setCurrentPosition(prev => {
        // Calculate distance to target position
        const dx = position.x - prev.x
        const dy = position.y - prev.y
        
        // If we're close enough, just set to final position
        if (Math.abs(dx) < 0.5 && Math.abs(dy) < 0.5) {
          return position
        }
        
        // Otherwise move a percentage of the way there (easing)
        return {
          x: prev.x + dx * 0.1,
          y: prev.y + dy * 0.1
        }
      })
      
      animationFrameRef.current = requestAnimationFrame(animateCursor)
    }
    
    animationFrameRef.current = requestAnimationFrame(animateCursor)
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [position, isVisible])

  useEffect(() => {
    if (isClicking) {
      // Add click ripple effect
      setClickRipples(prev => [...prev, currentPosition])
      
      // Remove ripple after animation
      const timer = setTimeout(() => {
        setClickRipples(prev => prev.slice(1))
        onClickComplete?.()
      }, 600)
      
      return () => clearTimeout(timer)
    }
  }, [isClicking, currentPosition, onClickComplete])

  useEffect(() => {
    // Simulate hover detection based on cursor movement
    const hoverTimer = setTimeout(() => {
      setIsHovering(true)
    }, 500)
    
    const unhoverTimer = setTimeout(() => {
      setIsHovering(false)
    }, 2000)

    return () => {
      clearTimeout(hoverTimer)
      clearTimeout(unhoverTimer)
    }
  }, [currentPosition])

  if (!isVisible) return null

  return (
    <>
      {/* Main Cursor */}
      <div
        className="absolute z-50 pointer-events-none"
        style={{
          left: `${currentPosition.x}px`,
          top: `${currentPosition.y}px`,
          transform: 'translate(-50%, -50%)'
        }}
      >
        <div className="relative">
          {/* Cursor body */}
          <div className={`w-6 h-6 bg-white rounded-full shadow-lg border-2 border-gray-800 transition-all duration-300 ${
            isClicking ? 'scale-90' : isHovering ? 'scale-110' : 'scale-100'
          } cursor-blink`} />
          
          {/* Cursor pointer */}
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-red-500 rounded-full" />
          
          {/* Hover glow effect */}
          {isHovering && (
            <div className="absolute inset-0 w-6 h-6 bg-yellow-400 rounded-full opacity-30 animate-pulse" />
          )}
          
          {/* Click indicator */}
          {isClicking && (
            <div className="absolute inset-0 w-6 h-6 bg-red-500 rounded-full opacity-50 animate-ping" />
          )}
        </div>
      </div>

      {/* Click Ripple Effects */}
      {clickRipples.map((ripple, index) => (
        <div
          key={index}
          className="absolute z-40 pointer-events-none"
          style={{
            left: `${ripple.x}px`,
            top: `${ripple.y}px`,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="w-8 h-8 border-2 border-red-500 rounded-full ripple-effect" />
          <div className="absolute inset-0 w-8 h-8 border-2 border-yellow-500 rounded-full ripple-effect animation-delay-150" />
        </div>
      ))}
    </>
  )
}

export default AnimatedCursor