import { z } from 'zod';

// Enhanced Zod schemas for roadmap-module integration
const UserPreferencesSchema = z.object({
  currentLevel: z.string(),
  learningGoal: z.string(),
  timeCommitment: z.string(),
  focusAreas: z.array(z.string())
});

const ModuleStatusSchema = z.enum(['locked', 'available', 'in-progress', 'completed']);

// Enhanced lesson plan schema for detailed planning
const LessonPlanSchema = z.object({
  id: z.string(),
  title: z.string(),
  learningObjectives: z.array(z.string()),
  skillTargets: z.array(z.string()),
  contentThemes: z.array(z.string()),
  vocabularyFocus: z.array(z.string()),
  grammarFocus: z.array(z.string()),
  culturalElements: z.array(z.string()).optional(),
  practiceActivities: z.array(z.string()),
  assessmentMethods: z.array(z.string()),
  prerequisites: z.array(z.string()),
  estimatedDuration: z.string(),
  difficultyLevel: z.number().min(1).max(5)
});

// Enhanced skill progression schema
const SkillProgressionSchema = z.object({
  skillId: z.string(),
  skillName: z.string(),
  category: z.string(), // 'reading', 'writing', 'speaking', 'listening', 'grammar', 'vocabulary'
  level: z.number().min(1).max(5),
  prerequisites: z.array(z.string()),
  dependentSkills: z.array(z.string()),
  masteryIndicators: z.array(z.string()),
  practiceActivities: z.array(z.string())
});

// Enhanced AI module schema with detailed lesson planning
const AIModuleSchema = z.object({
  id: z.string().min(1),
  title: z.string().min(1),
  description: z.string().min(1),
  duration: z.string().min(1),
  lessons: z.number().int().positive(),
  dependsOn: z.array(z.string()).default([]),
  // Enhanced fields for integration
  learningObjectives: z.array(z.string()),
  skillProgression: z.array(SkillProgressionSchema),
  lessonPlans: z.array(LessonPlanSchema),
  contentContext: z.object({
    vocabularyThemes: z.array(z.string()),
    grammarFocusAreas: z.array(z.string()),
    culturalElements: z.array(z.string()),
    practiceTypes: z.array(z.string())
  }),
  assessmentStrategy: z.object({
    formativeAssessments: z.array(z.string()),
    summativeAssessments: z.array(z.string()),
    progressIndicators: z.array(z.string())
  })
});

const ModuleSchema = AIModuleSchema.extend({
  status: ModuleStatusSchema,
  completionPercentage: z.number().min(0).max(100)
});

const AIResponseSchema = z.object({
  modules: z.array(AIModuleSchema).min(1).max(10),
  estimatedCompletionTime: z.string().min(1),
  personalizedTips: z.array(z.string()).min(1).max(5),
  // Enhanced roadmap context
  overallSkillProgression: z.array(SkillProgressionSchema),
  learningPathway: z.object({
    milestones: z.array(z.string()),
    checkpoints: z.array(z.string()),
    adaptationPoints: z.array(z.string())
  }),
  integrationMetadata: z.object({
    generatedAt: z.string(),
    version: z.string(),
    contextHash: z.string()
  })
});

// Enhanced type exports
type UserPreferences = z.infer<typeof UserPreferencesSchema>;
type Module = z.infer<typeof ModuleSchema>;
type AIResponse = z.infer<typeof AIResponseSchema>;
type AIModule = z.infer<typeof AIModuleSchema>;
type LessonPlan = z.infer<typeof LessonPlanSchema>;
type SkillProgression = z.infer<typeof SkillProgressionSchema>;

class AIService {
  private apiUrl = import.meta.env.VITE_BIGMODEL_API_URL || 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  private apiKey = import.meta.env.VITE_BIGMODEL_API_KEY;

  private createPrompt(preferences: UserPreferences): string {
    const userContext = this.analyzeUserContext(preferences);
    const contextualPrompt = this.generateContextualPrompt(userContext);
    
    return `You are an expert Japanese language learning curriculum designer with deep understanding of personalized learning paths and detailed lesson planning. Create a comprehensive learning roadmap with detailed lesson plans, skill progression mapping, and contextual learning information based on the user analysis below:

**Detailed User Profile:**
${this.formatUserProfile(preferences, userContext)}

**Contextual Learning Requirements:**
${contextualPrompt}

**Enhanced Roadmap Generation Guidelines:**
1. Create detailed lesson plans for each module with specific learning objectives
2. Map skill progression dependencies between lessons and modules
3. Define vocabulary themes and grammar focus areas for each lesson
4. Include cultural elements and practice activities
5. Establish assessment strategies and progress indicators
6. Ensure seamless integration between modules for cohesive learning
7. Provide comprehensive context for future module generation

**CRITICAL: You MUST respond with ONLY valid JSON. No explanations, no markdown, no extra text.**

**Required Enhanced JSON Format:**
{
  "modules": [
    {
      "id": "descriptive-kebab-case-id",
      "title": "Engaging Module Title",
      "description": "Detailed description explaining value and outcomes (2-3 sentences)",
      "duration": "X-Y weeks",
      "lessons": number,
      "dependsOn": ["prerequisite-module-ids"],
      "learningObjectives": ["specific measurable objectives"],
      "skillProgression": [
        {
          "skillId": "skill-identifier",
          "skillName": "Skill Name",
          "category": "reading|writing|speaking|listening|grammar|vocabulary",
          "level": 1-5,
          "prerequisites": ["prerequisite-skill-ids"],
          "dependentSkills": ["dependent-skill-ids"],
          "masteryIndicators": ["indicators of mastery"],
          "practiceActivities": ["specific practice activities"]
        }
      ],
      "lessonPlans": [
        {
          "id": "lesson-identifier",
          "title": "Lesson Title",
          "learningObjectives": ["specific lesson objectives"],
          "skillTargets": ["skills targeted in this lesson"],
          "contentThemes": ["main content themes"],
          "vocabularyFocus": ["vocabulary items to learn"],
          "grammarFocus": ["grammar points to cover"],
          "culturalElements": ["cultural aspects to include"],
          "practiceActivities": ["specific practice exercises"],
          "assessmentMethods": ["how to assess learning"],
          "prerequisites": ["required prior knowledge"],
          "estimatedDuration": "X minutes",
          "difficultyLevel": 1-5
        }
      ],
      "contentContext": {
        "vocabularyThemes": ["overarching vocabulary themes"],
        "grammarFocusAreas": ["main grammar areas"],
        "culturalElements": ["cultural context elements"],
        "practiceTypes": ["types of practice activities"]
      },
      "assessmentStrategy": {
        "formativeAssessments": ["ongoing assessment methods"],
        "summativeAssessments": ["end-of-module assessments"],
        "progressIndicators": ["indicators of progress"]
      }
    }
  ],
  "estimatedCompletionTime": "X-Y months (based on ${preferences.timeCommitment} daily)",
  "personalizedTips": ["contextual tip based on profile", "goal-specific advice", "learning style optimization"],
  "overallSkillProgression": [
    {
      "skillId": "overall-skill-id",
      "skillName": "Overall Skill Name",
      "category": "skill category",
      "level": 1-5,
      "prerequisites": [],
      "dependentSkills": [],
      "masteryIndicators": [],
      "practiceActivities": []
    }
  ],
  "learningPathway": {
    "milestones": ["major learning milestones"],
    "checkpoints": ["progress checkpoints"],
    "adaptationPoints": ["points where learning can be adapted"]
  },
  "integrationMetadata": {
    "generatedAt": "${new Date().toISOString()}",
    "version": "2.0.0",
    "contextHash": "${this.generateContextHash(preferences)}"
  }
}

**Requirements:**
- Generate 4-7 modules with 15-45 lessons each
- Create detailed lesson plans for each lesson
- Map skill dependencies across the entire roadmap
- Include comprehensive context for module generation
- Ensure proper JSON syntax with no trailing commas
- Use double quotes only
- Tailor everything to the user's unique learning profile

**RESPOND WITH ONLY THE JSON OBJECT:**`;
  }

  private analyzeUserContext(preferences: UserPreferences): any {
    return {
      proficiencyLevel: this.assessProficiencyLevel(preferences),
      learningVelocity: this.estimateLearningVelocity(preferences),
      motivationFactors: this.identifyMotivationFactors(preferences),
      challengeAreas: this.identifyChallengingAreas(preferences),
      strengths: this.identifyStrengths(preferences)
    };
  }

  private generateContextualPrompt(userContext: any): string {
    let prompt = "";
    
    if (userContext.proficiencyLevel === 'absolute-beginner') {
      prompt += "- Focus on foundational elements with extensive practice and repetition\n";
      prompt += "- Include confidence-building exercises and clear progress markers\n";
    }
    
    if (userContext.learningVelocity === 'accelerated') {
      prompt += "- Provide challenging content with faster progression\n";
      prompt += "- Include advanced exercises and optional enrichment materials\n";
    }
    
    if (userContext.motivationFactors.includes('practical-application')) {
      prompt += "- Emphasize real-world usage and practical scenarios\n";
      prompt += "- Include situational dialogues and cultural context\n";
    }
    
    return prompt;
  }

  private formatUserProfile(preferences: UserPreferences, userContext: any): string {
    return `- Current Level: ${preferences.currentLevel} (Assessed Proficiency: ${userContext.proficiencyLevel})
- Primary Goal: ${preferences.learningGoal}
- Daily Time Commitment: ${preferences.timeCommitment}
- Focus Areas: ${preferences.focusAreas.join(', ')}
- Estimated Learning Velocity: ${userContext.learningVelocity}
- Key Motivation Factors: ${userContext.motivationFactors.join(', ')}`;
  }

  private assessProficiencyLevel(preferences: UserPreferences): string {
    // More nuanced assessment based on multiple factors
    if (preferences.currentLevel === 'absolute-beginner') {
      return 'true-beginner';
    }
    if (preferences.currentLevel === 'beginner') {
      return 'motivated-beginner';
    }
    return preferences.currentLevel;
  }

  private estimateLearningVelocity(preferences: UserPreferences): string {
    if (preferences.timeCommitment === '2+ hours') {
      return 'accelerated';
    }
    if (preferences.timeCommitment === '15-30 minutes') {
      return 'steady';
    }
    return 'moderate';
  }

  private identifyMotivationFactors(preferences: UserPreferences): string[] {
    const factors = [];
    if (preferences.learningGoal.includes('travel')) factors.push('practical-application');
    if (preferences.learningGoal.includes('business')) factors.push('professional-development');
    if (preferences.learningGoal.includes('culture')) factors.push('cultural-interest');
    if (preferences.learningGoal.includes('anime') || preferences.learningGoal.includes('manga')) factors.push('media-consumption');
    return factors.length > 0 ? factors : ['general-interest'];
  }

  private identifyChallengingAreas(preferences: UserPreferences): string[] {
    const challenges = [];
    if (!preferences.focusAreas.includes('speaking')) challenges.push('oral-communication');
    if (!preferences.focusAreas.includes('writing')) challenges.push('written-expression');
    if (preferences.currentLevel === 'absolute-beginner') challenges.push('script-recognition');
    return challenges;
  }

  private identifyStrengths(preferences: UserPreferences): string[] {
    return preferences.focusAreas.map(area => area.replace('_', '-'));
  }

  private generateContextHash(preferences: UserPreferences): string {
    const contextString = JSON.stringify({
      level: preferences.currentLevel,
      goal: preferences.learningGoal,
      time: preferences.timeCommitment,
      focus: preferences.focusAreas.sort()
    });
    
    // Simple hash function for context identification
    let hash = 0;
    for (let i = 0; i < contextString.length; i++) {
      const char = contextString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(16);
  }

  private determineModuleStatus(moduleIndex: number, userLevel: string): {
    status: 'locked' | 'available' | 'in-progress' | 'completed';
    completionPercentage: number;
  } {
    // Dynamic status determination based on user level and module position
    const levelProgressMap = {
      'absolute-beginner': { completed: 0, inProgress: 1, available: 2 },
      'beginner': { completed: 1, inProgress: 2, available: 3 },
      'elementary': { completed: 2, inProgress: 3, available: 4 },
      'intermediate': { completed: 3, inProgress: 4, available: 5 },
      'advanced': { completed: 4, inProgress: 5, available: 6 }
    };
    
    const progress = levelProgressMap[userLevel as keyof typeof levelProgressMap] || levelProgressMap['absolute-beginner'];
    
    if (moduleIndex < progress.completed) {
      return { status: 'completed', completionPercentage: 100 };
    } else if (moduleIndex < progress.inProgress) {
      // Generate realistic progress for in-progress modules
      const progressPercentage = Math.floor(Math.random() * 60) + 20; // 20-80%
      return { status: 'in-progress', completionPercentage: progressPercentage };
    } else if (moduleIndex < progress.available) {
      return { status: 'available', completionPercentage: 0 };
    } else {
      return { status: 'locked', completionPercentage: 0 };
    }
  }

  async generateRoadmap(preferences: UserPreferences): Promise<AIResponse> {
    try {
      if (!this.apiKey) {
        console.warn('API key not found, using dynamic fallback roadmap');
        return this.generateDynamicFallback(preferences);
      }

      const prompt = this.createPrompt(preferences);
      
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'glm-4.5',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 20000
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const aiContent = data.choices[0]?.message?.content;
      
      if (!aiContent) {
        throw new Error('No content received from AI');
      }

      // Parse and validate the AI response using Zod
      let validatedResponse: z.infer<typeof AIResponseSchema>;
      try {
        // Extract JSON from the response (in case there's extra text)
        const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in AI response');
        }

        // Parse JSON
        const rawJson = JSON.parse(jsonMatch[0]);
        
        // Validate with Zod schema
        const parseResult = AIResponseSchema.safeParse(rawJson);
        
        if (!parseResult.success) {
          console.error('AI response validation failed:', parseResult.error.issues);
          console.error('Raw AI response:', aiContent);
          throw new Error(`Invalid AI response structure: ${parseResult.error.issues.map(i => i.message).join(', ')}`);
        }
        
        validatedResponse = parseResult.data;
        
      } catch (parseError) {
        console.error('Failed to parse/validate AI response:', parseError);
        console.error('Raw AI content:', aiContent);
        throw new Error('Invalid JSON response from AI');
      }

      // Add status and completion percentage to modules
      const modulesWithStatus: Module[] = validatedResponse.modules.map((module, index) => {
        const statusInfo = this.determineModuleStatus(index, preferences.currentLevel);
        return {
          ...module,
          ...statusInfo
        };
      });

      return {
        ...validatedResponse,
        modules: modulesWithStatus
      };

    } catch (error) {
      console.error('AI Service Error:', error);
      
      // Return dynamic fallback roadmap with proper structure
      return this.generateDynamicFallback(preferences);
    }
  }

  private generateDynamicFallback(preferences: UserPreferences): AIResponse {
    const userContext = {
      level: preferences.currentLevel,
      learningGoal: preferences.learningGoal,
      timeCommitment: preferences.timeCommitment,
      focusAreas: preferences.focusAreas
    };

    const modules = this.generateEnhancedContextualModules(userContext);
    const estimatedTime = this.calculateEstimatedTime(userContext);
    const personalizedTips = this.generatePersonalizedTips(userContext);
    const overallSkillProgression = this.generateOverallSkillProgression(userContext);
    const learningPathway = this.generateLearningPathway(userContext);

    return {
      estimatedCompletionTime: estimatedTime,
      personalizedTips,
      modules,
      overallSkillProgression,
      learningPathway,
      integrationMetadata: {
        generatedAt: new Date().toISOString(),
        version: '2.0.0',
        contextHash: this.generateContextHash(preferences)
      }
    };
  }

  private generateEnhancedContextualModules(userContext: any): Module[] {
    const baseModules = this.getEnhancedBaseModulesForLevel(userContext.level);
    const prioritizedModules = this.prioritizeModulesByGoals(baseModules, userContext.learningGoal);
    const customizedModules = this.customizeModulesByFocus(prioritizedModules, userContext.focusAreas);
    
    return customizedModules.map((module, index) => {
      const statusInfo = this.determineModuleStatus(index, userContext.level);
      const skillProgression = this.generateSkillProgressionForModule(module.id, userContext.level);
      const lessonPlans = this.generateLessonPlansForModule(module.id, module.lessons || 5);
      
      return {
        ...module,
        status: statusInfo.status,
        completionPercentage: statusInfo.completionPercentage,
        skillProgression,
        lessonPlans,
        contentContext: {
          ...module.contentContext,
          roadmapPosition: index + 1,
          totalModules: customizedModules.length,
          prerequisiteModules: customizedModules.slice(0, index).map(m => m.id),
          dependentModules: customizedModules.slice(index + 1).map(m => m.id)
        },
        assessmentStrategy: {
          ...module.assessmentStrategy,
          feedbackMechanisms: ['Immediate feedback', 'Progress tracking'],
          adaptiveDifficulty: true
        }
      };
    });
  }

  private generateContextualModules(userContext: any): Module[] {
    // Legacy method for backward compatibility
    return this.generateEnhancedContextualModules(userContext);
  }

  private getEnhancedBaseModulesForLevel(level: string): any[] {
    const enhancedModuleDatabase = {
      'absolute-beginner': [
        {
          id: 'hiragana-katakana',
          title: 'Hiragana & Katakana Mastery',
          description: 'Learn to read and write the two basic Japanese syllabaries with comprehensive practice and cultural context.',
          duration: '2-3 weeks',
          lessons: 15,
          dependsOn: [],
          learningObjectives: [
            'Master all 46 hiragana characters',
            'Master all 46 katakana characters',
            'Develop fluent reading speed',
            'Write characters with proper stroke order'
          ],
          skillProgression: this.generateSkillProgressionForModule('hiragana-katakana', level),
          lessonPlans: this.generateLessonPlansForModule('hiragana-katakana', 15),
          contentContext: {
            vocabularyThemes: ['Basic greetings', 'Numbers', 'Family terms', 'Common objects'],
            grammarFocusAreas: ['Character recognition', 'Pronunciation rules', 'Basic particles'],
            culturalElements: ['Writing system history', 'Calligraphy basics', 'Cultural significance'],
            practiceTypes: ['Character tracing', 'Reading exercises', 'Writing practice', 'Recognition games']
          },
          assessmentStrategy: {
            formativeAssessments: ['Daily character quizzes', 'Writing practice sheets', 'Reading speed tests'],
            summativeAssessments: ['Complete character recognition test', 'Timed writing assessment'],
            progressIndicators: ['Character accuracy rate', 'Reading fluency speed', 'Writing consistency']
          }
        },
        {
          id: 'basic-grammar',
          title: 'Basic Grammar Structures',
          description: 'Essential grammar patterns for building simple sentences with practical application.',
          duration: '3-4 weeks',
          lessons: 20,
          dependsOn: ['hiragana-katakana'],
          learningObjectives: [
            'Understand basic sentence structure (SOV)',
            'Use basic particles correctly',
            'Form simple present and past tense',
            'Ask and answer basic questions'
          ],
          skillProgression: this.generateSkillProgressionForModule('basic-grammar', level),
          lessonPlans: this.generateLessonPlansForModule('basic-grammar', 20),
          contentContext: {
            vocabularyThemes: ['Daily activities', 'Time expressions', 'Location words', 'Basic adjectives'],
            grammarFocusAreas: ['Particles (は、が、を、に)', 'Verb conjugation', 'Adjective forms', 'Question formation'],
            culturalElements: ['Politeness levels', 'Social context', 'Formal vs informal speech'],
            practiceTypes: ['Sentence building', 'Translation exercises', 'Conversation practice', 'Grammar drills']
          },
          assessmentStrategy: {
            formativeAssessments: ['Grammar pattern exercises', 'Sentence construction tasks', 'Particle usage quizzes'],
            summativeAssessments: ['Comprehensive grammar test', 'Conversation assessment'],
            progressIndicators: ['Grammar accuracy', 'Sentence complexity', 'Communication effectiveness']
          }
        }
      ],
      'beginner': [
        {
          id: 'grammar-expansion',
          title: 'Grammar Expansion',
          description: 'Build upon basic grammar with more complex structures and nuanced expressions.',
          duration: '4-5 weeks',
          lessons: 22,
          dependsOn: [],
          learningObjectives: [
            'Master intermediate grammar patterns',
            'Use conditional and potential forms',
            'Express opinions and preferences',
            'Handle complex sentence structures'
          ],
          skillProgression: this.generateSkillProgressionForModule('grammar-expansion', level),
          lessonPlans: this.generateLessonPlansForModule('grammar-expansion', 22),
          contentContext: {
            vocabularyThemes: ['Emotions', 'Opinions', 'Preferences', 'Complex actions'],
            grammarFocusAreas: ['Conditional forms', 'Potential forms', 'Comparative structures', 'Complex particles'],
            culturalElements: ['Expression of opinions', 'Indirect communication', 'Social harmony'],
            practiceTypes: ['Complex sentence building', 'Opinion expression', 'Conditional scenarios', 'Nuanced conversations']
          },
          assessmentStrategy: {
            formativeAssessments: ['Pattern recognition exercises', 'Contextual usage tasks', 'Expression practice'],
            summativeAssessments: ['Advanced grammar test', 'Opinion expression assessment'],
            progressIndicators: ['Pattern mastery', 'Expression fluency', 'Contextual accuracy']
          }
        }
      ],
      'elementary': [
        {
          id: 'intermediate-grammar',
          title: 'Intermediate Grammar',
          description: 'Complex sentence structures and advanced grammar patterns for sophisticated communication.',
          duration: '5-6 weeks',
          lessons: 28,
          dependsOn: [],
          learningObjectives: [
            'Master advanced grammar patterns',
            'Use honorific and humble forms',
            'Express complex relationships',
            'Handle formal communication'
          ],
          skillProgression: this.generateSkillProgressionForModule('intermediate-grammar', level),
          lessonPlans: this.generateLessonPlansForModule('intermediate-grammar', 28),
          contentContext: {
            vocabularyThemes: ['Professional terms', 'Academic language', 'Formal expressions', 'Complex concepts'],
            grammarFocusAreas: ['Honorific language', 'Humble forms', 'Complex conditionals', 'Advanced particles'],
            culturalElements: ['Business etiquette', 'Academic discourse', 'Formal situations'],
            practiceTypes: ['Formal communication', 'Academic writing', 'Professional scenarios', 'Complex discussions']
          },
          assessmentStrategy: {
            formativeAssessments: ['Formal language exercises', 'Complex pattern drills', 'Situational practice'],
            summativeAssessments: ['Comprehensive grammar evaluation', 'Formal communication test'],
            progressIndicators: ['Formal accuracy', 'Complex expression ability', 'Situational appropriateness']
          }
        }
      ],
      'intermediate': [
        {
          id: 'advanced-grammar',
          title: 'Advanced Grammar & Nuances',
          description: 'Master complex grammar and subtle language nuances for near-native communication.',
          duration: '6-8 weeks',
          lessons: 35,
          dependsOn: [],
          learningObjectives: [
            'Master subtle grammar nuances',
            'Use advanced literary forms',
            'Express complex abstract concepts',
            'Achieve near-native accuracy'
          ],
          skillProgression: this.generateSkillProgressionForModule('advanced-grammar', level),
          lessonPlans: this.generateLessonPlansForModule('advanced-grammar', 35),
          contentContext: {
            vocabularyThemes: ['Abstract concepts', 'Literary language', 'Specialized terminology', 'Nuanced expressions'],
            grammarFocusAreas: ['Literary forms', 'Advanced conditionals', 'Subtle distinctions', 'Register variations'],
            culturalElements: ['Literary culture', 'Academic traditions', 'Professional nuances'],
            practiceTypes: ['Literary analysis', 'Abstract discussions', 'Nuanced writing', 'Advanced presentations']
          },
          assessmentStrategy: {
            formativeAssessments: ['Nuance recognition tasks', 'Advanced pattern exercises', 'Literary analysis'],
            summativeAssessments: ['Comprehensive mastery test', 'Advanced communication evaluation'],
            progressIndicators: ['Nuance accuracy', 'Literary comprehension', 'Advanced expression fluency']
          }
        }
      ],
      'advanced': [
        {
          id: 'specialized-domains',
          title: 'Specialized Domains',
          description: 'Master specialized vocabulary and expressions for professional and academic contexts.',
          duration: '8-10 weeks',
          lessons: 50,
          dependsOn: [],
          learningObjectives: [
            'Master domain-specific vocabulary',
            'Use specialized expressions',
            'Handle professional communication',
            'Achieve expert-level fluency'
          ],
          skillProgression: this.generateSkillProgressionForModule('specialized-domains', level),
          lessonPlans: this.generateLessonPlansForModule('specialized-domains', 50),
          contentContext: {
            vocabularyThemes: ['Technical terminology', 'Professional jargon', 'Academic vocabulary', 'Specialized fields'],
            grammarFocusAreas: ['Technical writing', 'Professional communication', 'Academic discourse', 'Specialized registers'],
            culturalElements: ['Professional culture', 'Academic traditions', 'Industry practices'],
            practiceTypes: ['Technical writing', 'Professional presentations', 'Academic discussions', 'Specialized projects']
          },
          assessmentStrategy: {
            formativeAssessments: ['Domain-specific exercises', 'Professional scenarios', 'Technical writing tasks'],
            summativeAssessments: ['Specialized competency test', 'Professional communication evaluation'],
            progressIndicators: ['Technical accuracy', 'Professional fluency', 'Specialized comprehension']
          }
        }
      ]
    };
    
    return enhancedModuleDatabase[level as keyof typeof enhancedModuleDatabase] || enhancedModuleDatabase['absolute-beginner'];
  }

  private getBaseModulesForLevel(level: string): any[] {
    // Legacy method for backward compatibility
    return this.getEnhancedBaseModulesForLevel(level).map(module => ({
      id: module.id,
      title: module.title,
      description: module.description,
      duration: module.duration,
      lessons: module.lessons,
      dependsOn: module.dependsOn
    }));
  }

  private prioritizeModulesByGoals(modules: any[], learningGoal: string): any[] {
    // Reorder modules based on learning goals
    if (learningGoal.includes('conversation') || learningGoal.includes('speaking')) {
      return modules.sort((a, b) => {
        if (a.id.includes('conversation') || a.id.includes('speaking')) return -1;
        if (b.id.includes('conversation') || b.id.includes('speaking')) return 1;
        return 0;
      });
    }
    
    if (learningGoal.includes('reading') || learningGoal.includes('kanji')) {
      return modules.sort((a, b) => {
        if (a.id.includes('kanji') || a.id.includes('reading')) return -1;
        if (b.id.includes('kanji') || b.id.includes('reading')) return 1;
        return 0;
      });
    }
    
    return modules;
  }

  private customizeModulesByFocus(modules: any[], focusAreas: string[]): any[] {
    return modules.map(module => {
      // Customize module content based on focus areas
      if (focusAreas.includes('speaking') && module.id.includes('conversation')) {
        return {
          ...module,
          lessons: module.lessons + 5,
          duration: module.duration.replace(/\d+/, (match: string) => String(parseInt(match) + 1))
        };
      }
      
      if (focusAreas.includes('reading') && module.id.includes('kanji')) {
        return {
          ...module,
          lessons: module.lessons + 8,
          duration: module.duration.replace(/\d+/, (match: string) => String(parseInt(match) + 2))
        };
      }
      
      return module;
    });
  }

  private calculateEstimatedTime(userContext: any): string {
    const timeMultipliers = {
      '15-30 minutes': 1.5,
      '30-60 minutes': 1.0,
      '1-2 hours': 0.7,
      '2+ hours': 0.5
    };
    
    const baseMonths = userContext.level === 'absolute-beginner' ? 8 : 
                      userContext.level === 'beginner' ? 6 :
                      userContext.level === 'elementary' ? 5 :
                      userContext.level === 'intermediate' ? 4 : 3;
    
    const multiplier = timeMultipliers[userContext.timeCommitment as keyof typeof timeMultipliers] || 1.0;
    const adjustedMonths = Math.ceil(baseMonths * multiplier);
    
    return `${adjustedMonths}-${adjustedMonths + 2} months`;
  }

  private generatePersonalizedTips(userContext: any): string[] {
    const tips = [];
    
    // Level-based tips
    if (userContext.level === 'absolute-beginner') {
      tips.push('Start with hiragana and katakana as they form the foundation of Japanese writing.');
    }
    
    // Goal-based tips
    if (userContext.learningGoal.includes('conversation')) {
      tips.push('Practice speaking out loud daily, even if just to yourself.');
    }
    
    if (userContext.learningGoal.includes('reading')) {
      tips.push('Read simple Japanese texts daily to reinforce kanji learning.');
    }
    
    // Focus area tips
    if (userContext.focusAreas.includes('speaking')) {
      tips.push('Find a language exchange partner for regular conversation practice.');
    }
    
    if (userContext.focusAreas.includes('listening')) {
      tips.push('Watch Japanese content with subtitles matching your level.');
    }
    
    // Time commitment tips
    if (userContext.timeCommitment === '15-30 minutes') {
      tips.push('Consistency is key - even 15 minutes daily is better than cramming.');
    }
    
    // Default tips if not enough specific ones
    while (tips.length < 3) {
      const defaultTips = [
        'Use spaced repetition for vocabulary retention.',
        'Practice writing characters daily to build muscle memory.',
        'Immerse yourself in Japanese media matching your level.',
        'Set small, achievable daily goals to maintain motivation.'
      ];
      
      for (const tip of defaultTips) {
        if (!tips.includes(tip) && tips.length < 3) {
          tips.push(tip);
        }
      }
    }
    
    return tips.slice(0, 3);
  }

  private generateSkillProgressionForModule(moduleId: string, level: string): SkillProgression[] {
    const skillMaps: { [key: string]: SkillProgression[] } = {
      'hiragana-katakana': [
        {
          skillId: 'hiragana-recognition',
          skillName: 'Hiragana Character Recognition',
          category: 'reading',
          level: 1,
          prerequisites: [],
          dependentSkills: ['basic-reading', 'vocabulary-recognition'],
          masteryIndicators: ['Recognize all 46 hiragana characters', 'Read hiragana words fluently'],
          practiceActivities: ['Character flashcards', 'Reading exercises', 'Recognition games']
        },
        {
          skillId: 'katakana-recognition',
          skillName: 'Katakana Character Recognition',
          category: 'reading',
          level: 1,
          prerequisites: ['hiragana-recognition'],
          dependentSkills: ['foreign-word-reading', 'mixed-script-reading'],
          masteryIndicators: ['Recognize all 46 katakana characters', 'Read katakana words fluently'],
          practiceActivities: ['Character flashcards', 'Foreign word reading', 'Mixed script exercises']
        }
      ],
      'basic-grammar': [
        {
          skillId: 'basic-sentence-structure',
          skillName: 'Basic Sentence Structure',
          category: 'grammar',
          level: 1,
          prerequisites: ['hiragana-recognition'],
          dependentSkills: ['complex-sentences', 'conversation-skills'],
          masteryIndicators: ['Form simple SOV sentences', 'Use basic particles correctly'],
          practiceActivities: ['Sentence building exercises', 'Grammar drills', 'Translation practice']
        }
      ],
      'grammar-expansion': [
        {
          skillId: 'intermediate-grammar-patterns',
          skillName: 'Intermediate Grammar Patterns',
          category: 'grammar',
          level: 2,
          prerequisites: ['basic-sentence-structure'],
          dependentSkills: ['advanced-grammar', 'nuanced-expression'],
          masteryIndicators: ['Use conditional forms correctly', 'Express complex ideas'],
          practiceActivities: ['Pattern practice', 'Contextual exercises', 'Complex sentence building']
        }
      ]
    };

    return skillMaps[moduleId] || [
      {
        skillId: `${moduleId}-core-skill`,
        skillName: `Core ${moduleId.replace('-', ' ')} Skills`,
        category: 'general',
        level: level === 'absolute-beginner' ? 1 : level === 'beginner' ? 2 : 3,
        prerequisites: [],
        dependentSkills: [],
        masteryIndicators: ['Complete module objectives'],
        practiceActivities: ['Module-specific exercises']
      }
    ];
  }

  private generateLessonPlansForModule(moduleId: string, lessonCount: number): LessonPlan[] {
    const lessonPlans: LessonPlan[] = [];
    
    for (let i = 1; i <= Math.min(lessonCount, 5); i++) {
      lessonPlans.push({
        id: `${moduleId}-lesson-${i}`,
        title: `${moduleId.replace('-', ' ')} - Lesson ${i}`,
        learningObjectives: [
          `Master lesson ${i} content`,
          `Apply lesson ${i} skills`,
          `Progress toward module completion`
        ],
        skillTargets: [`${moduleId}-skill-${i}`],
        contentThemes: [`Theme ${i} for ${moduleId}`],
        vocabularyFocus: [`Vocabulary set ${i}`],
        grammarFocus: [`Grammar point ${i}`],
        culturalElements: [`Cultural aspect ${i}`],
        practiceActivities: [`Practice activity ${i}`, `Exercise set ${i}`],
        assessmentMethods: [`Assessment method ${i}`],
        prerequisites: i > 1 ? [`${moduleId}-lesson-${i-1}`] : [],
        estimatedDuration: '30 minutes',
        difficultyLevel: Math.min(i, 5)
      });
    }
    
    return lessonPlans;
  }

  private generateOverallSkillProgression(userContext: any): SkillProgression[] {
    const baseSkills: SkillProgression[] = [
      {
        skillId: 'reading-comprehension',
        skillName: 'Reading Comprehension',
        category: 'reading',
        level: userContext.level === 'absolute-beginner' ? 1 : 2,
        prerequisites: [],
        dependentSkills: ['advanced-reading', 'literature-analysis'],
        masteryIndicators: ['Read and understand texts at appropriate level'],
        practiceActivities: ['Reading exercises', 'Comprehension tests']
      },
      {
        skillId: 'speaking-fluency',
        skillName: 'Speaking Fluency',
        category: 'speaking',
        level: userContext.level === 'absolute-beginner' ? 1 : 2,
        prerequisites: [],
        dependentSkills: ['conversation-mastery', 'presentation-skills'],
        masteryIndicators: ['Speak clearly and confidently'],
        practiceActivities: ['Conversation practice', 'Speaking exercises']
      }
    ];

    return baseSkills;
  }

  private generateLearningPathway(userContext: any): { milestones: string[], checkpoints: string[], adaptationPoints: string[] } {
    return {
      milestones: [
        'Complete foundational skills',
        'Achieve conversational ability',
        'Master intermediate concepts',
        'Reach advanced proficiency'
      ],
      checkpoints: [
        'Weekly progress review',
        'Monthly skill assessment',
        'Quarterly goal evaluation'
      ],
      adaptationPoints: [
        'Adjust difficulty based on progress',
        'Modify focus areas based on interests',
        'Adapt pacing to learning speed'
      ]
    };
  }
}

export const aiService = new AIService();
export type { UserPreferences, Module, AIResponse, LessonPlan, SkillProgression };