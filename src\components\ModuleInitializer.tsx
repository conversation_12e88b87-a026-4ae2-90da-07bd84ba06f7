import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>ting<PERSON>, <PERSON>Open, Target, Clock, Users, Spark<PERSON>, CheckCircle, AlertCircle } from 'lucide-react'
import { useModuleStore } from '../stores/moduleStore'

interface ModuleInitializerProps {
  moduleId: string
  onInitialized: () => void
  onCancel: () => void
}

interface InitializationConfig {
  userLevel: string
  focusAreas: string[]
  lessonCount: number
  preferredTypes: string[]
  studyGoals: {
    dailyMinutes: number
    weeklyLessons: number
  }
}

const ModuleInitializer: React.FC<ModuleInitializerProps> = ({
  moduleId,
  onInitialized,
  onCancel
}) => {
  const { initializeModule, isLoading, error } = useModuleStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [config, setConfig] = useState<InitializationConfig>({
    userLevel: 'beginner',
    focusAreas: [],
    lessonCount: 5,
    preferredTypes: ['reading', 'practice'],
    studyGoals: {
      dailyMinutes: 30,
      weeklyLessons: 5
    }
  })
  const [isInitializing, setIsInitializing] = useState(false)
  const [initializationProgress, setInitializationProgress] = useState(0)

  // Dynamic module info based on moduleId
  const getModuleInfo = () => {
    const moduleTitle = moduleId.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
    
    const coreTopics = [
      'Reading', 'Writing', 'Speaking', 'Listening', 
      'Grammar', 'Vocabulary', 'Pronunciation', 'Culture'
    ];
    
    return {
      title: moduleTitle,
      description: `Master ${moduleTitle.toLowerCase()} with personalized lessons tailored to your learning style.`,
      coreTopics
    };
  }

  const moduleInfo = getModuleInfo()

  const handleLevelChange = (level: string) => {
    setConfig(prev => ({ ...prev, userLevel: level }))
  }

  const handleFocusAreaToggle = (area: string) => {
    setConfig(prev => ({
      ...prev,
      focusAreas: prev.focusAreas.includes(area)
        ? prev.focusAreas.filter(a => a !== area)
        : [...prev.focusAreas, area]
    }))
  }

  const handleLessonTypeToggle = (type: string) => {
    setConfig(prev => ({
      ...prev,
      preferredTypes: prev.preferredTypes.includes(type)
        ? prev.preferredTypes.filter(t => t !== type)
        : [...prev.preferredTypes, type]
    }))
  }

  const handleInitialize = async () => {
    setIsInitializing(true)
    setInitializationProgress(0)
    
    try {
      // Simulate initialization progress
      const progressSteps = [
        { message: 'Setting up module structure...', progress: 20 },
        { message: 'Analyzing your preferences...', progress: 40 },
        { message: 'Generating personalized lessons...', progress: 70 },
        { message: 'Finalizing configuration...', progress: 90 },
        { message: 'Ready to start learning!', progress: 100 }
      ]
      
      for (const step of progressSteps) {
        setInitializationProgress(step.progress)
        await new Promise(resolve => setTimeout(resolve, 800))
      }
      
      await initializeModule(moduleId, config.userLevel, config.focusAreas)
      onInitialized()
    } catch (error) {
      console.error('Failed to initialize module:', error)
    } finally {
      setIsInitializing(false)
    }
  }

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1: return config.userLevel !== ''
      case 2: return config.focusAreas.length > 0
      case 3: return config.preferredTypes.length > 0
      default: return false
    }
  }



  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg border border-gray-700 max-w-md w-full mx-4">
          <div className="text-center">
            <Sparkles className="w-12 h-12 text-yellow-400 mx-auto mb-4 animate-pulse" />
            <h2 className="text-xl font-semibold text-gray-100 mb-2">Initializing Module</h2>
            <p className="text-gray-400 mb-6">Setting up your personalized learning experience...</p>
            
            <div className="w-full bg-gray-700 rounded-full h-3 mb-4">
              <div 
                className="bg-gradient-to-r from-red-500 to-yellow-500 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${initializationProgress}%` }}
              ></div>
            </div>
            
            <p className="text-sm text-gray-500">{initializationProgress}% complete</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-red-400 mb-2">Module Setup</h1>
          <h2 className="text-xl text-gray-100 mb-4">{moduleInfo.title}</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">{moduleInfo.description}</p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8">
          {[1, 2, 3].map(step => (
            <React.Fragment key={step}>
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                step <= currentStep 
                  ? 'bg-red-600 border-red-600 text-white' 
                  : 'border-gray-600 text-gray-400'
              }`}>
                {step < currentStep ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="font-medium">{step}</span>
                )}
              </div>
              {step < 3 && (
                <div className={`w-16 h-0.5 transition-colors ${
                  step < currentStep ? 'bg-red-600' : 'bg-gray-600'
                }`}></div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Step Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 mb-8">
          {currentStep === 1 && (
            <div>
              <h3 className="text-xl font-semibold text-yellow-400 mb-6 flex items-center gap-2">
                <Users className="w-5 h-5" />
                What's your current Japanese level?
              </h3>
              
              <div className="grid md:grid-cols-2 gap-4">
                {[
                  {
                    id: 'absolute-beginner',
                    title: 'Absolute Beginner',
                    description: 'I\'m completely new to Japanese'
                  },
                  {
                    id: 'beginner',
                    title: 'Beginner',
                    description: 'I know some basic words and phrases'
                  },
                  {
                    id: 'elementary',
                    title: 'Elementary',
                    description: 'I can read hiragana/katakana and know basic grammar'
                  },
                  {
                    id: 'intermediate',
                    title: 'Intermediate',
                    description: 'I can have simple conversations and read basic texts'
                  }
                ].map(level => (
                  <button
                    key={level.id}
                    onClick={() => handleLevelChange(level.id)}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      config.userLevel === level.id
                        ? 'border-red-500 bg-red-900/20'
                        : 'border-gray-600 hover:border-gray-500 bg-gray-700'
                    }`}
                  >
                    <h4 className="font-medium text-gray-100 mb-1">{level.title}</h4>
                    <p className="text-sm text-gray-400">{level.description}</p>
                  </button>
                ))}
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div>
              <h3 className="text-xl font-semibold text-yellow-400 mb-6 flex items-center gap-2">
                <Target className="w-5 h-5" />
                What would you like to focus on?
              </h3>
              
              <p className="text-gray-400 mb-6">Select the areas you're most interested in learning (choose 2-4):</p>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                {moduleInfo.coreTopics.map(topic => (
                  <button
                    key={topic}
                    onClick={() => handleFocusAreaToggle(topic)}
                    className={`p-3 rounded-lg border text-left transition-all ${
                      config.focusAreas.includes(topic)
                        ? 'border-red-500 bg-red-900/20 text-red-100'
                        : 'border-gray-600 hover:border-gray-500 bg-gray-700 text-gray-300'
                    }`}
                  >
                    <span className="text-sm">{topic}</span>
                  </button>
                ))}
              </div>
              
              <div className="mt-4 text-sm text-gray-500">
                Selected: {config.focusAreas.length} areas
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <h3 className="text-xl font-semibold text-yellow-400 mb-6 flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Customize your learning experience
              </h3>
              
              <div className="space-y-6">
                {/* Lesson Types */}
                <div>
                  <h4 className="font-medium text-gray-100 mb-3">Preferred lesson types:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {[
                      { id: 'reading', label: 'Reading', icon: BookOpen },
                      { id: 'practice', label: 'Practice', icon: Target },
                      { id: 'quiz', label: 'Quiz', icon: CheckCircle },
                      { id: 'video', label: 'Video', icon: Play },
                      { id: 'conversation', label: 'Conversation', icon: Users }
                    ].map(type => {
                      const Icon = type.icon
                      return (
                        <button
                          key={type.id}
                          onClick={() => handleLessonTypeToggle(type.id)}
                          className={`p-3 rounded-lg border text-center transition-all ${
                            config.preferredTypes.includes(type.id)
                              ? 'border-red-500 bg-red-900/20 text-red-100'
                              : 'border-gray-600 hover:border-gray-500 bg-gray-700 text-gray-300'
                          }`}
                        >
                          <Icon className="w-5 h-5 mx-auto mb-1" />
                          <span className="text-sm">{type.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>
                
                {/* Initial Lesson Count */}
                <div>
                  <h4 className="font-medium text-gray-100 mb-3">Initial lessons to generate:</h4>
                  <div className="flex items-center gap-4">
                    <input
                      type="range"
                      min="3"
                      max="10"
                      value={config.lessonCount}
                      onChange={(e) => setConfig(prev => ({ ...prev, lessonCount: parseInt(e.target.value) }))}
                      className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-gray-100 font-medium w-8">{config.lessonCount}</span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">More lessons will be generated as you progress</p>
                </div>
                
                {/* Study Goals */}
                <div>
                  <h4 className="font-medium text-gray-100 mb-3">Study goals:</h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Daily study time (minutes)</label>
                      <select
                        value={config.studyGoals.dailyMinutes}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          studyGoals: { ...prev.studyGoals, dailyMinutes: parseInt(e.target.value) }
                        }))}
                        className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-gray-100 focus:border-red-400 focus:outline-none"
                      >
                        <option value={15}>15 minutes</option>
                        <option value={30}>30 minutes</option>
                        <option value={45}>45 minutes</option>
                        <option value={60}>1 hour</option>
                        <option value={90}>1.5 hours</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm text-gray-400 mb-2">Weekly lessons target</label>
                      <select
                        value={config.studyGoals.weeklyLessons}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          studyGoals: { ...prev.studyGoals, weeklyLessons: parseInt(e.target.value) }
                        }))}
                        className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-gray-100 focus:border-red-400 focus:outline-none"
                      >
                        <option value={3}>3 lessons</option>
                        <option value={5}>5 lessons</option>
                        <option value={7}>7 lessons</option>
                        <option value={10}>10 lessons</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <div className="flex gap-3">
            <button
              onClick={onCancel}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-100 font-medium transition-colors rounded"
            >
              Cancel
            </button>
            
            {currentStep > 1 && (
              <button
                onClick={prevStep}
                className="px-6 py-3 bg-gray-600 hover:bg-gray-500 text-gray-100 font-medium transition-colors rounded"
              >
                Previous
              </button>
            )}
          </div>
          
          <div className="flex gap-3">
            {currentStep < 3 ? (
              <button
                onClick={nextStep}
                disabled={!canProceed()}
                className="px-6 py-3 bg-red-600 hover:bg-red-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium transition-colors rounded"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleInitialize}
                disabled={!canProceed() || isLoading}
                className="px-8 py-3 bg-gradient-to-r from-red-600 to-yellow-600 hover:from-red-500 hover:to-yellow-500 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-medium transition-all rounded flex items-center gap-2"
              >
                <Sparkles className="w-4 h-4" />
                {isLoading ? 'Initializing...' : 'Start Learning'}
              </button>
            )}
          </div>
        </div>

        {error && (
          <div className="mt-4 p-4 bg-red-900/20 border border-red-500 rounded-lg">
            <div className="flex items-center gap-2 text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span className="font-medium">Initialization Error</span>
            </div>
            <p className="text-red-300 mt-1">{error}</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ModuleInitializer