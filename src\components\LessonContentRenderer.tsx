import React, { useState } from 'react'
import { ChevronDown, ChevronUp, BookOpen, PenTool, Volume2, Eye, EyeOff } from 'lucide-react'
import type { LessonContent } from '../stores/moduleStore'

interface LessonContentRendererProps {
  content: LessonContent
  lessonType: 'reading' | 'practice' | 'quiz' | 'video' | 'conversation'
  onExerciseComplete?: (exerciseIndex: number, correct: boolean) => void
  onVocabularyStudied?: (termIndex: number) => void
}

const LessonContentRenderer: React.FC<LessonContentRendererProps> = ({
  content,
  lessonType,
  onExerciseComplete,
  onVocabularyStudied
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['introduction', 'mainContent']))
  const [exerciseAnswers, setExerciseAnswers] = useState<{ [key: number]: string }>({})
  const [showAnswers, setShowAnswers] = useState<{ [key: number]: boolean }>({})
  const [studiedVocab, setStudiedVocab] = useState<Set<number>>(new Set())

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const handleExerciseAnswer = (exerciseIndex: number, answer: string) => {
    setExerciseAnswers(prev => ({ ...prev, [exerciseIndex]: answer }))
  }

  const toggleAnswerVisibility = (exerciseIndex: number) => {
    setShowAnswers(prev => ({ ...prev, [exerciseIndex]: !prev[exerciseIndex] }))
    
    if (onExerciseComplete && !showAnswers[exerciseIndex]) {
      const userAnswer = exerciseAnswers[exerciseIndex] || ''
      const correctAnswer = content.exercises?.[exerciseIndex]?.answer || ''
      const isCorrect = userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim()
      onExerciseComplete(exerciseIndex, isCorrect)
    }
  }

  const markVocabStudied = (termIndex: number) => {
    const newStudied = new Set(studiedVocab)
    newStudied.add(termIndex)
    setStudiedVocab(newStudied)
    
    if (onVocabularyStudied) {
      onVocabularyStudied(termIndex)
    }
  }

  const playPronunciation = (text: string) => {
    // This would integrate with a text-to-speech service
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = 'ja-JP'
      speechSynthesis.speak(utterance)
    }
  }

  const renderSection = (title: string, sectionKey: string, children: React.ReactNode) => {
    const isExpanded = expandedSections.has(sectionKey)
    
    return (
      <div className="mb-6">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="flex items-center justify-between w-full text-left mb-3 group"
        >
          <h3 className="text-xl font-semibold text-yellow-500 group-hover:text-yellow-400 transition-colors">
            {title}
          </h3>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-gray-400 group-hover:text-gray-300" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-400 group-hover:text-gray-300" />
          )}
        </button>
        
        {isExpanded && (
          <div className="transition-all duration-200 ease-in-out">
            {children}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-gray-800 p-8 rounded-lg border-l-4 border-red-600">
      {/* Introduction Section */}
      {content.introduction && (
        renderSection('Introduction', 'introduction', (
          <div className="text-gray-300 leading-relaxed bg-gray-700 p-4 rounded border-l-2 border-blue-500">
            <BookOpen className="w-5 h-5 inline mr-2 text-blue-400" />
            {content.introduction}
          </div>
        ))
      )}

      {/* Main Content Section */}
      {renderSection('Lesson Content', 'mainContent', (
        <div className="text-gray-300 leading-relaxed whitespace-pre-line">
          {content.mainContent}
        </div>
      ))}

      {/* Examples Section */}
      {content.examples && content.examples.length > 0 && (
        renderSection('Examples', 'examples', (
          <div className="space-y-3">
            {content.examples.map((example, index) => (
              <div key={index} className="bg-gray-700 p-4 rounded border-l-2 border-yellow-600 group">
                <div className="flex items-start justify-between">
                  <code className="text-gray-100 flex-1">{example}</code>
                  <button
                    onClick={() => playPronunciation(example)}
                    className="ml-3 p-1 text-gray-400 hover:text-yellow-400 transition-colors opacity-0 group-hover:opacity-100"
                    title="Play pronunciation"
                  >
                    <Volume2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ))
      )}

      {/* Vocabulary Section */}
      {content.vocabulary && content.vocabulary.length > 0 && (
        renderSection('Vocabulary', 'vocabulary', (
          <div className="grid gap-4 md:grid-cols-2">
            {content.vocabulary.map((vocab, index) => (
              <div 
                key={index} 
                className={`bg-gray-700 p-4 rounded border-l-2 transition-all duration-200 ${
                  studiedVocab.has(index) 
                    ? 'border-green-500 bg-gray-600' 
                    : 'border-purple-500 hover:border-purple-400'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-lg font-medium text-gray-100">{vocab.term}</span>
                      <button
                        onClick={() => playPronunciation(vocab.reading)}
                        className="p-1 text-gray-400 hover:text-purple-400 transition-colors"
                        title="Play pronunciation"
                      >
                        <Volume2 className="w-3 h-3" />
                      </button>
                    </div>
                    <div className="text-sm text-purple-300 mb-1">{vocab.reading}</div>
                    <div className="text-sm text-gray-300">{vocab.meaning}</div>
                  </div>
                  <button
                    onClick={() => markVocabStudied(index)}
                    className={`ml-2 p-1 rounded transition-colors ${
                      studiedVocab.has(index)
                        ? 'text-green-400 bg-green-900'
                        : 'text-gray-400 hover:text-green-400 hover:bg-gray-600'
                    }`}
                    title={studiedVocab.has(index) ? 'Studied' : 'Mark as studied'}
                  >
                    <PenTool className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ))
      )}

      {/* Grammar Section */}
      {content.grammar && content.grammar.length > 0 && (
        renderSection('Grammar Patterns', 'grammar', (
          <div className="space-y-4">
            {content.grammar.map((grammar, index) => (
              <div key={index} className="bg-gray-700 p-4 rounded border-l-2 border-orange-500">
                <div className="mb-3">
                  <div className="text-lg font-medium text-orange-300 mb-2">{grammar.pattern}</div>
                  <div className="text-gray-300 mb-3">{grammar.explanation}</div>
                </div>
                {grammar.examples && grammar.examples.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-orange-400 mb-2">Examples:</div>
                    <div className="space-y-1">
                      {grammar.examples.map((example, exampleIndex) => (
                        <div key={exampleIndex} className="text-sm text-gray-300 bg-gray-600 p-2 rounded flex items-center justify-between">
                          <code>{example}</code>
                          <button
                            onClick={() => playPronunciation(example)}
                            className="ml-2 p-1 text-gray-400 hover:text-orange-400 transition-colors"
                            title="Play pronunciation"
                          >
                            <Volume2 className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ))
      )}

      {/* Exercises Section */}
      {content.exercises && content.exercises.length > 0 && (
        renderSection('Practice Exercises', 'exercises', (
          <div className="space-y-4">
            {content.exercises.map((exercise, index) => (
              <div key={index} className="bg-gray-700 p-4 rounded border-l-2 border-green-500">
                <div className="mb-3">
                  <div className="font-medium text-gray-100 mb-2">{exercise.question}</div>
                  
                  {lessonType === 'practice' || lessonType === 'quiz' ? (
                    <div className="space-y-2">
                      <input
                        type="text"
                        value={exerciseAnswers[index] || ''}
                        onChange={(e) => handleExerciseAnswer(index, e.target.value)}
                        placeholder="Type your answer here..."
                        className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-gray-100 focus:border-green-400 focus:outline-none"
                      />
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => toggleAnswerVisibility(index)}
                          className="flex items-center gap-1 px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-sm rounded transition-colors"
                        >
                          {showAnswers[index] ? (
                            <><EyeOff className="w-3 h-3" /> Hide Answer</>
                          ) : (
                            <><Eye className="w-3 h-3" /> Check Answer</>
                          )}
                        </button>
                        {showAnswers[index] && (
                          <div className="text-sm">
                            {exerciseAnswers[index] && (
                              <span className={`px-2 py-1 rounded ${
                                exerciseAnswers[index].toLowerCase().trim() === exercise.answer.toLowerCase().trim()
                                  ? 'bg-green-600 text-green-100'
                                  : 'bg-red-600 text-red-100'
                              }`}>
                                {exerciseAnswers[index].toLowerCase().trim() === exercise.answer.toLowerCase().trim() 
                                  ? 'Correct!' 
                                  : 'Incorrect'
                                }
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <details className="text-gray-400">
                      <summary className="cursor-pointer hover:text-gray-300 select-none">Show Answer</summary>
                      <div className="mt-2 p-2 bg-gray-600 rounded text-gray-100">{exercise.answer}</div>
                    </details>
                  )}
                </div>
                
                {showAnswers[index] && (
                  <div className="mt-3 p-3 bg-gray-600 rounded">
                    <div className="text-sm font-medium text-green-400 mb-1">Correct Answer:</div>
                    <div className="text-gray-100">{exercise.answer}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ))
      )}
    </div>
  )
}

export default LessonContentRenderer