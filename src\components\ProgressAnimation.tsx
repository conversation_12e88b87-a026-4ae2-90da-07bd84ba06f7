import React, { useState, useEffect } from 'react'
import { CheckCircle } from 'lucide-react'

interface ProgressAnimationProps {
  isVisible: boolean
  currentProgress: number
  targetProgress: number
  duration?: number
}

const ProgressAnimation: React.FC<ProgressAnimationProps> = ({
  isVisible,
  currentProgress,
  targetProgress,
  duration = 1000
}) => {
  const [animatedProgress, setAnimatedProgress] = useState(currentProgress)
  const [showCheckmark, setShowCheckmark] = useState(false)

  useEffect(() => {
    if (!isVisible) return

    const startTime = Date.now()
    const startProgress = animatedProgress
    const progressDiff = targetProgress - startProgress

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      const newProgress = startProgress + (progressDiff * easeOutCubic)
      
      setAnimatedProgress(newProgress)
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setAnimatedProgress(targetProgress)
        if (targetProgress >= 100) {
          setShowCheckmark(true)
        }
      }
    }

    requestAnimationFrame(animate)
  }, [isVisible, targetProgress, duration])

  if (!isVisible) return null

  return (
    <div className="flex items-center space-x-3">
      <div className="flex-1 bg-gray-700 rounded-full h-3 overflow-hidden relative">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full seigaiha-pattern"></div>
        </div>
        
        {/* Progress bar */}
        <div 
          className="h-full bg-gradient-to-r from-red-500 via-red-400 to-yellow-500 transition-all duration-300 relative overflow-hidden"
          style={{ width: `${animatedProgress}%` }}
        >
          {/* Animated shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
          
          {/* Progress particles */}
          {animatedProgress > 0 && (
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
              <div className="w-1 h-1 bg-yellow-300 rounded-full animate-ping"></div>
            </div>
          )}
        </div>
      </div>
      
      {/* Progress percentage */}
      <div className="flex items-center space-x-2 min-w-[60px]">
        {showCheckmark ? (
          <CheckCircle className="w-5 h-5 text-green-500 animate-in fade-in duration-500" />
        ) : (
          <span className="text-sm font-medium text-gray-300">
            {Math.round(animatedProgress)}%
          </span>
        )}
      </div>
    </div>
  )
}

export default ProgressAnimation