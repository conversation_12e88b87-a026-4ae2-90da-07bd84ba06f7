import { useEffect, useState } from 'react'
import { Link, useParams, useNavigate } from 'react-router-dom'
import { Plus, BarChart3, Settings, RefreshCw, Target, TrendingUp } from 'lucide-react'
import { useModuleStore } from '../stores/moduleStore'
import { progressTracker } from '../services/progressTracker'
import { progressiveLearningTracker } from '../services/progressiveLearningTracker'
import LessonContentRenderer from '../components/LessonContentRenderer'
import ProgressDashboard from '../components/ProgressDashboard'
import ModuleInitializer from '../components/ModuleInitializer'
import type { Lesson, ModuleState } from '../stores/moduleStore'

const ModuleLessons = () => {
  const { moduleId } = useParams<{ moduleId: string }>()
  const navigate = useNavigate()
  const { 
    modules, 
    currentModuleId, 
    isLoading, 
    error, 
    initializeModule, 
    generateLessons, 
    markLessonComplete, 
    setCurrentModule,
    getModuleById 
  } = useModuleStore()
  
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0)
  const [showProgressDashboard, setShowProgressDashboard] = useState(false)
  const [showInitializer, setShowInitializer] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [isGeneratingLessons, setIsGeneratingLessons] = useState(false)
  const [enhancedModuleData, setEnhancedModuleData] = useState<any>(null)
  const [learningPathway, setLearningPathway] = useState<any>(null)
  const [progressiveData, setProgressiveData] = useState<any>(null)
  const [adaptationRecommendations, setAdaptationRecommendations] = useState<any[]>([])
  
  const module = moduleId ? getModuleById(moduleId) : null

  useEffect(() => {
    if (!moduleId) {
      navigate('/roadmap')
      return
    }

    setCurrentModule(moduleId)
    
    // Load enhanced module data if available
    const storedModules = localStorage.getItem('generatedModules')
    const storedPathway = localStorage.getItem('learningPathway')
    
    if (storedModules) {
      try {
        const modules = JSON.parse(storedModules)
        if (modules[moduleId]) {
          setEnhancedModuleData(modules[moduleId])
        }
      } catch (error) {
        console.error('Error loading enhanced module data:', error)
      }
    }
    
    if (storedPathway) {
      try {
        const pathway = JSON.parse(storedPathway)
        setLearningPathway(pathway)
        
        // Get progressive learning data
        const progressData = progressiveLearningTracker.updateProgress(pathway.id, {
          moduleId,
          progressDelta: 0,
          timestamp: new Date().toISOString()
        })
        setProgressiveData(progressData)
        
        // Get adaptation recommendations
        const recommendations = progressiveLearningTracker.getPersonalizedRecommendations(pathway.id)
        setAdaptationRecommendations([recommendations])
      } catch (error) {
        console.error('Error loading learning pathway:', error)
      }
    }
    
    // Check if module exists and is initialized
    const existingModule = getModuleById(moduleId)
    if (!existingModule || !existingModule.isInitialized) {
      setShowInitializer(true)
    } else {
      // Set current lesson index based on progress
      setCurrentLessonIndex(existingModule.progress.currentLessonIndex)
    }
  }, [moduleId, navigate, setCurrentModule, getModuleById])

  // Start a learning session when a lesson begins
  const startLearningSession = (lessonId: string) => {
    if (moduleId) {
      const sessionId = progressTracker.startSession(moduleId, lessonId)
      setCurrentSessionId(sessionId)
    }
  }

  // End the learning session when lesson is completed
  const endLearningSession = (completed: boolean, score?: number) => {
    if (currentSessionId) {
      progressTracker.endSession(currentSessionId, completed, score)
      setCurrentSessionId(null)
    }
  }

  const handleGenerateMoreLessons = async () => {
    if (!moduleId || !module) return
    
    setIsGeneratingLessons(true)
    try {
      await generateLessons(moduleId, 3) // Generate 3 more lessons
    } catch (error) {
      console.error('Failed to generate more lessons:', error)
    } finally {
      setIsGeneratingLessons(false)
    }
  }

  const handleModuleInitialized = () => {
    setShowInitializer(false)
    // Refresh module data
    const updatedModule = getModuleById(moduleId!)
    if (updatedModule) {
      setCurrentLessonIndex(0)
    }
  }

  const currentLesson = module?.lessons[currentLessonIndex]

  const nextLesson = () => {
    if (module && currentLessonIndex < module.lessons.length - 1) {
      // End current session if active
      if (currentSessionId) {
        endLearningSession(true)
      }
      
      const nextIndex = currentLessonIndex + 1
      setCurrentLessonIndex(nextIndex)
      
      // Start new session for next lesson
      startLearningSession(module.lessons[nextIndex].id)
    }
  }

  const prevLesson = () => {
    if (currentLessonIndex > 0) {
      // End current session if active
      if (currentSessionId) {
        endLearningSession(false)
      }
      
      const prevIndex = currentLessonIndex - 1
      setCurrentLessonIndex(prevIndex)
      
      // Start new session for previous lesson
      if (module) {
        startLearningSession(module.lessons[prevIndex].id)
      }
    }
  }

  const markComplete = async () => {
    if (module && moduleId) {
      const currentLesson = module.lessons[currentLessonIndex]
      
      // End learning session with completion
      endLearningSession(true, 85) // Default score of 85%
      
      // Mark lesson as complete in store
      await markLessonComplete(moduleId, currentLesson.id)
      
      // Update progressive learning tracker if pathway exists
      if (learningPathway && enhancedModuleData) {
        const skillsAcquired = enhancedModuleData.skillProgression?.targetSkills?.slice(0, 1) || []
        const objectivesCompleted = enhancedModuleData.learningObjectives?.slice(0, 1) || []
        
        const updatedProgress = progressiveLearningTracker.updateProgress(learningPathway.id, {
          moduleId,
          progressDelta: 10, // Assume 10% progress per lesson
          timestamp: new Date().toISOString()
        })
        
        setProgressiveData(updatedProgress)
        
        // Get updated recommendations
        const newRecommendations = progressiveLearningTracker.getPersonalizedRecommendations(learningPathway.id)
        setAdaptationRecommendations([newRecommendations])
      }
      
      // Auto-advance to next lesson if available
      if (currentLessonIndex < module.lessons.length - 1) {
        setTimeout(() => {
          nextLesson()
        }, 1000)
      }
    }
  }

  // Show module initializer if module is not initialized
  if (showInitializer) {
    return (
      <div className="min-h-screen bg-gray-900">
        <ModuleInitializer 
          moduleId={moduleId!}
          onInitialized={handleModuleInitialized}
          onCancel={() => navigate('/roadmap')}
        />
      </div>
    )
  }

  // Show progress dashboard if requested
  if (showProgressDashboard) {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-gray-100">Learning Analytics</h1>
            <button
              onClick={() => setShowProgressDashboard(false)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Back to Lessons
            </button>
          </div>
          <ProgressDashboard />
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => navigate('/roadmap')}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Back to Roadmap
          </button>
        </div>
      </div>
    )
  }

  if (!module || !currentLesson) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-red-400 mb-4">Module not found</h2>
          <Link to="/roadmap" className="text-yellow-500 hover:text-yellow-400">Return to Roadmap</Link>
        </div>
      </div>
    )
  }

  // Start session for current lesson if not already started
  if (!currentSessionId && currentLesson) {
    startLearningSession(currentLesson.id)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-6xl mx-auto px-6 py-4 flex justify-between items-center">
          <Link to="/roadmap" className="text-red-400 hover:text-red-300 transition-colors">
            ← Back to Roadmap
          </Link>
          <div className="text-center">
            <h1 className="text-xl font-semibold text-gray-100">{module.title}</h1>
            <div className="text-sm text-gray-400">
              Lesson {currentLessonIndex + 1} of {module.lessons.length}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {/* Action Buttons */}
            <button
              onClick={() => setShowProgressDashboard(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <BarChart3 className="w-4 h-4" />
              <span>Analytics</span>
            </button>
            
            <button
              onClick={handleGenerateMoreLessons}
              disabled={isGeneratingLessons}
              className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isGeneratingLessons ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Plus className="w-4 h-4" />
              )}
              <span>{isGeneratingLessons ? 'Generating...' : 'More Lessons'}</span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar - Lesson List */}
        <aside className="w-80 bg-gray-800 border-r border-gray-700 min-h-screen">
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-4 text-red-400">Lessons</h2>
            <div className="space-y-2">
              {module.lessons.map((lesson, index) => (
                <button
                  key={lesson.id}
                  onClick={() => setCurrentLessonIndex(index)}
                  className={`w-full text-left p-3 rounded transition-colors ${
                    index === currentLessonIndex
                      ? 'bg-red-600 text-white'
                      : lesson.completed
                      ? 'bg-gray-700 text-gray-100 hover:bg-gray-600'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{lesson.title}</div>
                      <div className="text-sm opacity-75">{lesson.duration}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        lesson.type === 'reading' ? 'bg-blue-600' :
                        lesson.type === 'practice' ? 'bg-green-600' :
                        lesson.type === 'quiz' ? 'bg-purple-600' :
                        'bg-orange-600'
                      }`}>
                        {lesson.type}
                      </span>
                      {lesson.completed && (
                        <span className="text-yellow-500">✓</span>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            {/* Lesson Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold mb-2 text-red-500">{currentLesson.title}</h1>
              <div className="flex items-center gap-4 text-gray-400">
                <span className={`px-3 py-1 rounded text-sm ${
                  currentLesson.type === 'reading' ? 'bg-blue-600' :
                  currentLesson.type === 'practice' ? 'bg-green-600' :
                  currentLesson.type === 'quiz' ? 'bg-purple-600' :
                  'bg-orange-600'
                }`}>
                  {currentLesson.type}
                </span>
                <span>{currentLesson.duration}</span>
                {currentLesson.completed && (
                  <span className="text-yellow-500 flex items-center gap-1">
                    ✓ Completed
                  </span>
                )}
              </div>
            </div>

            {/* Enhanced Lesson Content */}
            <LessonContentRenderer 
              content={currentLesson.content}
              lessonType={currentLesson.type}
            />

            {/* Lesson Actions */}
            <div className="flex justify-between items-center mb-8">
              <button
                onClick={prevLesson}
                disabled={currentLessonIndex === 0}
                className="px-6 py-3 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium transition-colors"
                style={{ clipPath: 'polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%)' }}
              >
                ← Previous Lesson
              </button>

              <div className="flex gap-4">
                {!currentLesson.completed && (
                  <button
                    onClick={markComplete}
                    className="px-6 py-3 bg-yellow-600 hover:bg-yellow-500 text-yellow-100 font-medium transition-colors"
                    style={{ clipPath: 'polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%)' }}
                  >
                    Mark Complete
                  </button>
                )}

                <button
                  onClick={nextLesson}
                  disabled={currentLessonIndex === module.lessons.length - 1}
                  className="px-6 py-3 bg-red-600 hover:bg-red-500 disabled:bg-gray-800 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium transition-colors"
                  style={{ clipPath: 'polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%)' }}
                >
                  Next Lesson →
                </button>
              </div>
            </div>

            {/* Enhanced Module Information */}
            {enhancedModuleData && (
              <div className="bg-gray-800 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-400" />
                  Enhanced Module Features
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {enhancedModuleData.skillProgression && (
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Skill Progression</div>
                      <div className="space-y-2">
                        <div className="text-gray-100 text-sm">
                          <span className="font-medium">Target Skills:</span> {enhancedModuleData.skillProgression.targetSkills?.length || 0}
                        </div>
                        <div className="text-gray-100 text-sm">
                          <span className="font-medium">Prerequisites:</span> {enhancedModuleData.skillProgression.prerequisites?.length || 0}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {enhancedModuleData.learningObjectives && (
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Learning Objectives</div>
                      <div className="text-gray-100 text-sm">
                        <span className="font-medium">Total:</span> {enhancedModuleData.learningObjectives.length}
                      </div>
                      <div className="mt-2 space-y-1">
                        {enhancedModuleData.learningObjectives.slice(0, 2).map((objective: any, index: number) => (
                          <div key={index} className="text-xs text-gray-300 flex items-start gap-1">
                            <span className="text-blue-400 mt-1">•</span>
                            <span>{objective.description || objective}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {enhancedModuleData.integrationMetadata && (
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Integration Info</div>
                      <div className="space-y-1 text-sm">
                        <div className="text-gray-100">
                          <span className="font-medium">Position:</span> {enhancedModuleData.integrationMetadata.roadmapPosition}
                        </div>
                        <div className="text-gray-100">
                          <span className="font-medium">Connected:</span> {enhancedModuleData.integrationMetadata.connectedModules?.length || 0}
                        </div>
                        <div className="text-gray-100">
                          <span className="font-medium">Adaptation:</span> {enhancedModuleData.integrationMetadata.adaptationLevel}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* Progressive Learning Insights */}
            {progressiveData && (
              <div className="bg-gray-800 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  Progressive Learning Insights
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">
                      {Math.round(progressiveData.overallProgress * 100)}%
                    </div>
                    <div className="text-sm text-gray-400">Overall Progress</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">
                      {progressiveData.skillMastery?.size || 0}
                    </div>
                    <div className="text-sm text-gray-400">Skills Mastered</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">
                      {progressiveData.objectiveProgress?.size || 0}
                    </div>
                    <div className="text-sm text-gray-400">Objectives Complete</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">
                      {adaptationRecommendations.length}
                    </div>
                    <div className="text-sm text-gray-400">Recommendations</div>
                  </div>
                </div>
                
                {adaptationRecommendations.length > 0 && (
                  <div className="mt-6">
                    <div className="text-gray-400 text-sm mb-3">Personalized Recommendations</div>
                    <div className="space-y-2">
                      {adaptationRecommendations.slice(0, 3).map((rec, index) => (
                        <div key={index} className="bg-gray-700 p-3 rounded-lg">
                          <div className="text-gray-100 text-sm font-medium">{rec.type}</div>
                          <div className="text-gray-300 text-xs mt-1">{rec.description}</div>
                          <div className="text-gray-500 text-xs mt-1">Priority: {rec.priority}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {/* Progress Summary */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Module Progress</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {module.progress.completedLessons.length}
                  </div>
                  <div className="text-sm text-gray-400">Lessons Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {Math.round(module.progress.progressPercentage)}%
                  </div>
                  <div className="text-sm text-gray-400">Overall Progress</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {module.progress.totalTimeSpent}m
                  </div>
                  <div className="text-sm text-gray-400">Time Spent</div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

export default ModuleLessons