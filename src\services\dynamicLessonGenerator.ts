import { z } from 'zod';

// Enhanced schemas for dynamic lesson generation
const LessonContentSchema = z.object({
  introduction: z.string(),
  mainContent: z.string(),
  examples: z.array(z.string()),
  exercises: z.array(z.object({
    question: z.string(),
    answer: z.string(),
    type: z.enum(['multiple-choice', 'fill-blank', 'translation', 'matching']).optional()
  })),
  vocabulary: z.array(z.object({
    term: z.string(),
    reading: z.string(),
    meaning: z.string()
  })).optional(),
  grammar: z.array(z.object({
    pattern: z.string(),
    explanation: z.string(),
    examples: z.array(z.string())
  })).optional()
});

const GeneratedLessonSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.enum(['reading', 'practice', 'quiz', 'video', 'conversation']),
  duration: z.string(),
  content: LessonContentSchema,
  difficulty: z.number().min(1).max(5),
  prerequisites: z.array(z.string()).optional()
});

const LessonGenerationResponseSchema = z.array(GeneratedLessonSchema);

export interface Lesson {
  id: string;
  title: string;
  type: 'reading' | 'practice' | 'quiz' | 'video' | 'conversation';
  duration: string;
  completed: boolean;
  content: {
    introduction: string;
    mainContent: string;
    examples: string[];
    exercises: { question: string; answer: string; type?: string }[];
    vocabulary?: { term: string; reading: string; meaning: string }[];
    grammar?: { pattern: string; explanation: string; examples: string[] }[];
  };
  difficulty: number;
  prerequisites: string[];
  aiGenerated: boolean;
  generatedAt: Date;
}

export interface LessonGenerationOptions {
  moduleId: string;
  moduleTitle: string;
  moduleDescription: string;
  userLevel: string;
  userGoals: string[];
  focusAreas: string[];
  learningStyle: string;
  lessonCount: number;
  startIndex: number;
  specificTopics?: string[];
  previousLessons?: Lesson[];
}

export interface ModuleContext {
  id: string;
  title: string;
  description: string;
  learningObjectives: string[];
  skillsToMaster: string[];
  culturalContext?: string[];
}

class DynamicLessonGeneratorService {
  private readonly API_ENDPOINT = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  private readonly API_KEY = import.meta.env.VITE_AI_API_KEY || 'your-api-key-here';

  async generateLessons(options: LessonGenerationOptions): Promise<Lesson[]> {
    try {
      // Generate dynamic module context
      const moduleContext = await this.generateModuleContext(options);
      
      // Create contextual prompt
      const prompt = this.buildContextualPrompt(options, moduleContext);
      
      // Call AI service
      const aiResponse = await this.callAI(prompt);
      
      // Parse and validate response
      const lessons = await this.parseAndValidateLessons(aiResponse, options);
      
      return lessons;
    } catch (error) {
      console.error('Error generating lessons:', error);
      
      // Generate dynamic fallback instead of static templates
      return this.generateDynamicFallback(options);
    }
  }

  private async generateModuleContext(options: LessonGenerationOptions): Promise<ModuleContext> {
    // Dynamically determine module context based on user profile and module info
    const learningObjectives = this.deriveLearningObjectives(options);
    const skillsToMaster = this.deriveSkillsToMaster(options);
    const culturalContext = this.deriveCulturalContext(options);

    return {
      id: options.moduleId,
      title: options.moduleTitle,
      description: options.moduleDescription,
      learningObjectives,
      skillsToMaster,
      culturalContext
    };
  }

  private deriveLearningObjectives(options: LessonGenerationOptions): string[] {
    const objectives = [];
    
    // Base objectives from module type
    if (options.moduleId.includes('hiragana') || options.moduleId.includes('katakana')) {
      objectives.push('Master reading and writing of Japanese syllabaries');
      objectives.push('Develop muscle memory for character formation');
    }
    
    if (options.moduleId.includes('grammar')) {
      objectives.push('Understand fundamental sentence structures');
      objectives.push('Apply grammar patterns in practical contexts');
    }
    
    if (options.moduleId.includes('vocabulary')) {
      objectives.push('Acquire essential vocabulary for daily communication');
      objectives.push('Develop word association and retention strategies');
    }
    
    if (options.moduleId.includes('conversation')) {
      objectives.push('Practice real-world communication scenarios');
      objectives.push('Build confidence in spoken Japanese');
    }
    
    // Add user-specific objectives
    if (options.focusAreas.includes('speaking')) {
      objectives.push('Improve pronunciation and speaking fluency');
    }
    
    if (options.focusAreas.includes('reading')) {
      objectives.push('Enhance reading comprehension skills');
    }
    
    return objectives;
  }

  private deriveSkillsToMaster(options: LessonGenerationOptions): string[] {
    const skills = [];
    
    // Level-appropriate skills
    if (options.userLevel === 'absolute-beginner') {
      skills.push('Character recognition', 'Basic pronunciation', 'Simple sentence formation');
    } else if (options.userLevel === 'beginner') {
      skills.push('Grammar application', 'Vocabulary expansion', 'Basic conversation');
    } else if (options.userLevel === 'elementary') {
      skills.push('Complex sentence structures', 'Kanji recognition', 'Contextual understanding');
    }
    
    // Module-specific skills
    if (options.moduleId.includes('kanji')) {
      skills.push('Stroke order mastery', 'Radical recognition', 'Multiple readings');
    }
    
    return skills;
  }

  private deriveCulturalContext(options: LessonGenerationOptions): string[] {
    const context = [];
    
    if (options.userGoals.some(goal => goal.includes('travel'))) {
      context.push('Travel etiquette', 'Regional customs', 'Tourist interactions');
    }
    
    if (options.userGoals.some(goal => goal.includes('business'))) {
      context.push('Business etiquette', 'Professional communication', 'Workplace culture');
    }
    
    if (options.userGoals.some(goal => goal.includes('culture'))) {
      context.push('Traditional customs', 'Modern society', 'Cultural nuances');
    }
    
    return context;
  }

  private buildContextualPrompt(options: LessonGenerationOptions, moduleContext: ModuleContext): string {
    const userProfile = this.buildUserProfile(options);
    const moduleAnalysis = this.buildModuleAnalysis(moduleContext);
    const lessonRequirements = this.buildLessonRequirements(options);
    const progressionLogic = this.buildProgressionLogic(options);
    
    return `You are an expert Japanese language curriculum designer specializing in personalized, adaptive learning experiences. Generate ${options.lessonCount} highly customized lessons for this specific learning context:

**User Profile & Learning Context:**
${userProfile}

**Module Analysis & Objectives:**
${moduleAnalysis}

**Lesson Generation Requirements:**
${lessonRequirements}

**Progression & Difficulty Logic:**
${progressionLogic}

**Advanced Customization Guidelines:**
1. Adapt content complexity to user's proficiency and learning velocity
2. Integrate user's focus areas throughout lesson content
3. Include learning style-appropriate activities and explanations
4. Provide cultural context relevant to user's goals
5. Ensure each lesson builds meaningfully on previous knowledge
6. Create engaging, practical content that maintains motivation

**Response Format - Return ONLY valid JSON:**
[{
  "id": "${options.moduleId}-lesson-${options.startIndex + 1}",
  "title": "Engaging, descriptive lesson title",
  "type": "reading|practice|quiz|video|conversation",
  "duration": "realistic time estimate",
  "content": {
    "introduction": "Motivating introduction explaining lesson value",
    "mainContent": "Comprehensive explanation with clear structure",
    "examples": ["practical, relevant examples"],
    "exercises": [{"question": "engaging practice question", "answer": "clear answer", "type": "exercise type"}],
    "vocabulary": [{"term": "Japanese term", "reading": "pronunciation", "meaning": "English meaning"}],
    "grammar": [{"pattern": "grammar pattern", "explanation": "clear explanation", "examples": ["usage examples"]}]
  },
  "difficulty": number (1-5, appropriate for user level),
  "prerequisites": ["previous lesson dependencies"]
}]

Generate lessons that are specifically tailored to this user's unique learning profile and provide maximum educational value.`;
  }

  private buildUserProfile(options: LessonGenerationOptions): string {
    return `- Current Level: ${options.userLevel}
- Learning Goals: ${options.userGoals.join(', ')}
- Focus Areas: ${options.focusAreas.join(', ')}
- Learning Style: ${options.learningStyle}
- Lesson Position: ${options.startIndex + 1}-${options.startIndex + options.lessonCount} in module
- Previous Context: ${options.previousLessons ? `Building on ${options.previousLessons.length} completed lessons` : 'Starting fresh module'}`;
  }

  private buildModuleAnalysis(moduleContext: ModuleContext): string {
    return `- Module: ${moduleContext.title}
- Description: ${moduleContext.description}
- Learning Objectives: ${moduleContext.learningObjectives.join(', ')}
- Skills to Master: ${moduleContext.skillsToMaster.join(', ')}
- Cultural Context: ${moduleContext.culturalContext?.join(', ') || 'General Japanese culture'}`;
  }

  private buildLessonRequirements(options: LessonGenerationOptions): string {
    let requirements = `- Generate exactly ${options.lessonCount} lessons
- Start lesson numbering from ${options.startIndex + 1}
- Include diverse lesson types appropriate for content
- Provide 3-5 practical examples per lesson
- Include 3-4 varied exercises per lesson
- Add relevant vocabulary (5-8 terms per lesson)
- Include grammar patterns when applicable`;
    
    if (options.specificTopics && options.specificTopics.length > 0) {
      requirements += `\n- Cover specific topics: ${options.specificTopics.join(', ')}`;
    }
    
    return requirements;
  }

  private buildProgressionLogic(options: LessonGenerationOptions): string {
    return `- Ensure logical difficulty progression across lessons
- Each lesson should build upon previous knowledge
- Introduce new concepts gradually with adequate practice
- Reinforce previous learning while adding new elements
- Maintain appropriate challenge level for ${options.userLevel} learners
- Include review and consolidation opportunities`;
  }

  private async callAI(prompt: string): Promise<string> {
    const response = await fetch(this.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.API_KEY}`
      },
      body: JSON.stringify({
        model: 'glm-4-plus',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from AI');
    }

    return content;
  }

  private async parseAndValidateLessons(aiResponse: string, options: LessonGenerationOptions): Promise<Lesson[]> {
    try {
      // Clean the response to extract JSON
      const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON array found in AI response');
      }

      const jsonString = jsonMatch[0];
      const rawLessons = JSON.parse(jsonString);
      
      // Validate with Zod schema
      const validatedLessons = LessonGenerationResponseSchema.parse(rawLessons);
      
      // Convert to Lesson objects
      const lessons: Lesson[] = validatedLessons.map((lessonData, index) => ({
        id: lessonData.id || `${options.moduleId}-lesson-${options.startIndex + index + 1}`,
        title: lessonData.title,
        type: lessonData.type,
        duration: lessonData.duration,
        completed: false,
        content: {
          introduction: lessonData.content.introduction || '',
          mainContent: lessonData.content.mainContent,
          examples: lessonData.content.examples || [],
          exercises: lessonData.content.exercises || [],
          vocabulary: lessonData.content.vocabulary || [],
          grammar: lessonData.content.grammar || []
        },
        difficulty: lessonData.difficulty,
        prerequisites: lessonData.prerequisites || [],
        aiGenerated: true,
        generatedAt: new Date()
      }));

      return lessons;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      console.error('Raw AI response:', aiResponse);
      throw new Error('Failed to parse AI-generated lessons');
    }
  }

  private generateDynamicFallback(options: LessonGenerationOptions): Lesson[] {
    const lessons: Lesson[] = [];
    
    for (let i = 0; i < options.lessonCount; i++) {
      const lessonNumber = options.startIndex + i + 1;
      const lesson = this.createContextualFallbackLesson(lessonNumber, options);
      lessons.push(lesson);
    }
    
    return lessons;
  }

  private createContextualFallbackLesson(lessonNumber: number, options: LessonGenerationOptions): Lesson {
    const lessonType = this.determineLessonType(lessonNumber, options);
    const difficulty = this.calculateDifficulty(lessonNumber, options);
    const topic = this.generateContextualTopic(lessonNumber, options);
    
    return {
      id: `${options.moduleId}-lesson-${lessonNumber}`,
      title: `Lesson ${lessonNumber}: ${topic}`,
      type: lessonType,
      duration: this.estimateDuration(lessonType, options.userLevel),
      completed: false,
      content: {
        introduction: `Welcome to lesson ${lessonNumber}. In this lesson, we'll explore ${topic} as part of your ${options.moduleTitle} journey.`,
        mainContent: this.generateContextualContent(topic, options),
        examples: this.generateContextualExamples(topic, options),
        exercises: this.generateContextualExercises(topic, options),
        vocabulary: this.generateContextualVocabulary(topic, options),
        grammar: lessonType === 'reading' || lessonType === 'practice' ? this.generateContextualGrammar(topic, options) : []
      },
      difficulty,
      prerequisites: lessonNumber > 1 ? [`${options.moduleId}-lesson-${lessonNumber - 1}`] : [],
      aiGenerated: false,
      generatedAt: new Date()
    };
  }

  private determineLessonType(lessonNumber: number, options: LessonGenerationOptions): 'reading' | 'practice' | 'quiz' | 'video' | 'conversation' {
    // Distribute lesson types based on user preferences and module content
    const types: ('reading' | 'practice' | 'quiz' | 'video' | 'conversation')[] = ['reading', 'practice'];
    
    if (options.focusAreas.includes('speaking')) {
      types.push('conversation');
    }
    
    if (options.learningStyle.includes('visual')) {
      types.push('video');
    }
    
    // Add quiz every 3-4 lessons for assessment
    if (lessonNumber % 4 === 0) {
      types.push('quiz');
    }
    
    return types[lessonNumber % types.length];
  }

  private calculateDifficulty(lessonNumber: number, options: LessonGenerationOptions): number {
    const baseDifficulty = options.userLevel === 'absolute-beginner' ? 1 :
                          options.userLevel === 'beginner' ? 2 :
                          options.userLevel === 'elementary' ? 3 : 4;
    
    const progressionBonus = Math.floor((lessonNumber - 1) / 5);
    return Math.min(baseDifficulty + progressionBonus, 5);
  }

  private generateContextualTopic(lessonNumber: number, options: LessonGenerationOptions): string {
    // Generate topics based on module type and user goals
    const baseTopics = this.getBaseTopicsForModule(options.moduleId);
    const userFocusedTopics = this.adaptTopicsToUserGoals(baseTopics, options.userGoals);
    
    return userFocusedTopics[(lessonNumber - 1) % userFocusedTopics.length];
  }

  private getBaseTopicsForModule(moduleId: string): string[] {
    if (moduleId.includes('hiragana')) {
      return ['Basic Hiragana Characters', 'Hiragana Combinations', 'Reading Practice', 'Writing Practice', 'Recognition Drills'];
    }
    
    if (moduleId.includes('grammar')) {
      return ['Sentence Structure', 'Particles Usage', 'Verb Conjugation', 'Adjective Forms', 'Question Formation'];
    }
    
    if (moduleId.includes('vocabulary')) {
      return ['Daily Objects', 'Family Terms', 'Food & Dining', 'Transportation', 'Time Expressions'];
    }
    
    return ['Japanese Fundamentals', 'Language Basics', 'Communication Skills', 'Cultural Context', 'Practical Application'];
  }

  private adaptTopicsToUserGoals(baseTopics: string[], userGoals: string[]): string[] {
    if (userGoals.some(goal => goal.includes('travel'))) {
      return baseTopics.map(topic => `${topic} for Travel`);
    }
    
    if (userGoals.some(goal => goal.includes('business'))) {
      return baseTopics.map(topic => `${topic} in Business Context`);
    }
    
    return baseTopics;
  }

  private estimateDuration(lessonType: string, userLevel: string): string {
    const baseDuration = userLevel === 'absolute-beginner' ? 20 :
                        userLevel === 'beginner' ? 18 :
                        userLevel === 'elementary' ? 15 : 12;
    
    const typeMultiplier = lessonType === 'quiz' ? 0.7 :
                          lessonType === 'conversation' ? 1.3 :
                          lessonType === 'video' ? 1.2 : 1.0;
    
    return `${Math.round(baseDuration * typeMultiplier)} min`;
  }

  private generateContextualContent(topic: string, options: LessonGenerationOptions): string {
    return `This lesson focuses on ${topic} within the context of ${options.moduleTitle}. You'll learn essential concepts that align with your goal of ${options.userGoals.join(' and ')}. The content is designed for ${options.userLevel} learners and emphasizes ${options.focusAreas.join(', ')} skills.`;
  }

  private generateContextualExamples(topic: string, options: LessonGenerationOptions): string[] {
    return [
      `Practical example of ${topic} in daily situations`,
      `${topic} usage in ${options.userGoals[0] || 'general'} context`,
      `Cultural application of ${topic} concepts`
    ];
  }

  private generateContextualExercises(topic: string, options: LessonGenerationOptions): { question: string; answer: string; type?: string }[] {
    return [
      {
        question: `Practice exercise for ${topic} - identify the correct usage`,
        answer: `Correct application of ${topic} principles`,
        type: 'multiple-choice'
      },
      {
        question: `Apply ${topic} in a practical scenario`,
        answer: `Contextual application example`,
        type: 'fill-blank'
      }
    ];
  }

  private generateContextualVocabulary(topic: string, options: LessonGenerationOptions): { term: string; reading: string; meaning: string }[] {
    return [
      {
        term: `${topic}関連語1`,
        reading: 'kanrengo1',
        meaning: `Key term related to ${topic}`
      },
      {
        term: `${topic}関連語2`,
        reading: 'kanrengo2',
        meaning: `Important concept in ${topic}`
      }
    ];
  }

  private generateContextualGrammar(topic: string, options: LessonGenerationOptions): { pattern: string; explanation: string; examples: string[] }[] {
    return [
      {
        pattern: `Grammar pattern for ${topic}`,
        explanation: `This pattern is used when discussing ${topic} concepts`,
        examples: [`Example 1 using ${topic}`, `Example 2 with ${topic}`]
      }
    ];
  }
}

export const dynamicLessonGenerator = new DynamicLessonGeneratorService();