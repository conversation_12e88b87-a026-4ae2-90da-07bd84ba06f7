import { z } from 'zod';
import type { UserPreferences, Module, SkillProgression, LessonPlan } from './aiService';
import type { GeneratedModule } from './dynamicModuleGenerator';

// Schemas for progressive learning tracking
const SkillDependencySchema = z.object({
  skillId: z.string(),
  dependsOn: z.array(z.string()),
  enablesSkills: z.array(z.string()),
  masteryThreshold: z.number().min(0).max(1),
  currentMastery: z.number().min(0).max(1),
  isBlocked: z.boolean(),
  blockingReasons: z.array(z.string())
});

const LearningObjectiveSchema = z.object({
  objectiveId: z.string(),
  description: z.string(),
  moduleId: z.string(),
  skillTargets: z.array(z.string()),
  priority: z.enum(['high', 'medium', 'low']),
  status: z.enum(['not-started', 'in-progress', 'completed', 'mastered']),
  progressPercentage: z.number().min(0).max(100),
  estimatedCompletionTime: z.string(),
  actualCompletionTime: z.string().optional(),
  alignmentScore: z.number().min(0).max(1)
});

const ProgressMilestoneSchema = z.object({
  milestoneId: z.string(),
  title: z.string(),
  description: z.string(),
  requiredSkills: z.array(z.string()),
  requiredObjectives: z.array(z.string()),
  isAchieved: z.boolean(),
  achievedAt: z.string().optional(),
  nextMilestones: z.array(z.string()),
  rewardType: z.enum(['badge', 'certificate', 'unlock', 'bonus-content'])
});

const LearningPathwaySchema = z.object({
  pathwayId: z.string(),
  userPreferences: z.any(), // UserPreferences type
  modules: z.array(z.string()),
  skillDependencies: z.array(SkillDependencySchema),
  learningObjectives: z.array(LearningObjectiveSchema),
  progressMilestones: z.array(ProgressMilestoneSchema),
  overallProgress: z.number().min(0).max(100),
  estimatedCompletionDate: z.string(),
  adaptationHistory: z.array(z.object({
    timestamp: z.string(),
    changeType: z.string(),
    reason: z.string(),
    impact: z.string()
  }))
});

type SkillDependency = z.infer<typeof SkillDependencySchema>;
type LearningObjective = z.infer<typeof LearningObjectiveSchema>;
type ProgressMilestone = z.infer<typeof ProgressMilestoneSchema>;
type LearningPathway = z.infer<typeof LearningPathwaySchema>;

interface ProgressUpdate {
  moduleId: string;
  skillId?: string;
  objectiveId?: string;
  progressDelta: number;
  timestamp: string;
  evidence?: string[];
}

interface AdaptationRecommendation {
  type: 'difficulty-adjustment' | 'content-modification' | 'pacing-change' | 'skill-reinforcement';
  target: string; // module, skill, or objective ID
  reason: string;
  suggestedAction: string;
  priority: 'high' | 'medium' | 'low';
  estimatedImpact: number;
}

class ProgressiveLearningTracker {
  private learningPathways: Map<string, LearningPathway> = new Map();
  private progressHistory: Map<string, ProgressUpdate[]> = new Map();
  private adaptationRules: Map<string, (pathway: LearningPathway) => AdaptationRecommendation[]> = new Map();

  constructor() {
    this.initializeAdaptationRules();
  }

  /**
   * Create a new learning pathway with skill dependencies and objectives
   */
  createLearningPathway(userPreferences: UserPreferences, modules: Module[]): LearningPathway {
    const pathwayId = this.generatePathwayId(userPreferences);
    
    const skillDependencies = this.buildSkillDependencyGraph(modules);
    const learningObjectives = this.extractLearningObjectives(modules);
    const progressMilestones = this.generateProgressMilestones(modules, skillDependencies);
    
    const pathway: LearningPathway = {
      pathwayId,
      userPreferences,
      modules: modules.map(m => m.id),
      skillDependencies,
      learningObjectives,
      progressMilestones,
      overallProgress: 0,
      estimatedCompletionDate: this.calculateEstimatedCompletion(modules, userPreferences),
      adaptationHistory: []
    };
    
    this.learningPathways.set(pathwayId, pathway);
    this.progressHistory.set(pathwayId, []);
    
    return pathway;
  }

  /**
   * Update progress for a specific skill or objective
   */
  updateProgress(pathwayId: string, update: ProgressUpdate): {
    success: boolean;
    newProgress: number;
    unlockedSkills: string[];
    achievedMilestones: string[];
    adaptationRecommendations: AdaptationRecommendation[];
  } {
    const pathway = this.learningPathways.get(pathwayId);
    if (!pathway) {
      return {
        success: false,
        newProgress: 0,
        unlockedSkills: [],
        achievedMilestones: [],
        adaptationRecommendations: []
      };
    }

    // Record progress update
    const history = this.progressHistory.get(pathwayId) || [];
    history.push(update);
    this.progressHistory.set(pathwayId, history);

    // Update skill mastery
    const unlockedSkills = this.updateSkillMastery(pathway, update);
    
    // Update objective progress
    this.updateObjectiveProgress(pathway, update);
    
    // Check for achieved milestones
    const achievedMilestones = this.checkMilestoneAchievements(pathway);
    
    // Update overall progress
    const newProgress = this.calculateOverallProgress(pathway);
    pathway.overallProgress = newProgress;
    
    // Generate adaptation recommendations
    const adaptationRecommendations = this.generateAdaptationRecommendations(pathway);
    
    return {
      success: true,
      newProgress,
      unlockedSkills,
      achievedMilestones,
      adaptationRecommendations
    };
  }

  /**
   * Check if a skill is ready to be learned (dependencies met)
   */
  isSkillReady(pathwayId: string, skillId: string): {
    isReady: boolean;
    missingPrerequisites: string[];
    readinessScore: number;
  } {
    const pathway = this.learningPathways.get(pathwayId);
    if (!pathway) {
      return { isReady: false, missingPrerequisites: [], readinessScore: 0 };
    }

    const skillDep = pathway.skillDependencies.find(sd => sd.skillId === skillId);
    if (!skillDep) {
      return { isReady: true, missingPrerequisites: [], readinessScore: 1 };
    }

    const missingPrerequisites = skillDep.dependsOn.filter(prereqId => {
      const prereqSkill = pathway.skillDependencies.find(sd => sd.skillId === prereqId);
      return !prereqSkill || prereqSkill.currentMastery < prereqSkill.masteryThreshold;
    });

    const readinessScore = skillDep.dependsOn.length === 0 ? 1 : 
      (skillDep.dependsOn.length - missingPrerequisites.length) / skillDep.dependsOn.length;

    return {
      isReady: missingPrerequisites.length === 0,
      missingPrerequisites,
      readinessScore
    };
  }

  /**
   * Get personalized learning recommendations
   */
  getPersonalizedRecommendations(pathwayId: string): {
    nextSkills: string[];
    priorityObjectives: LearningObjective[];
    suggestedModules: string[];
    adaptationNeeded: boolean;
    recommendations: AdaptationRecommendation[];
  } {
    const pathway = this.learningPathways.get(pathwayId);
    if (!pathway) {
      return {
        nextSkills: [],
        priorityObjectives: [],
        suggestedModules: [],
        adaptationNeeded: false,
        recommendations: []
      };
    }

    // Find next ready skills
    const nextSkills = pathway.skillDependencies
      .filter(sd => !sd.isBlocked && sd.currentMastery < sd.masteryThreshold)
      .filter(sd => this.isSkillReady(pathwayId, sd.skillId).isReady)
      .map(sd => sd.skillId)
      .slice(0, 3);

    // Get priority objectives
    const priorityObjectives = pathway.learningObjectives
      .filter(obj => obj.status === 'not-started' || obj.status === 'in-progress')
      .filter(obj => obj.priority === 'high')
      .slice(0, 5);

    // Suggest next modules
    const suggestedModules = this.getSuggestedModules(pathway, nextSkills);

    // Check if adaptation is needed
    const recommendations = this.generateAdaptationRecommendations(pathway);
    const adaptationNeeded = recommendations.some(rec => rec.priority === 'high');

    return {
      nextSkills,
      priorityObjectives,
      suggestedModules,
      adaptationNeeded,
      recommendations
    };
  }

  /**
   * Analyze learning patterns and suggest optimizations
   */
  analyzeLearningPatterns(pathwayId: string): {
    learningVelocity: number;
    strongAreas: string[];
    challengingAreas: string[];
    optimalStudyTime: string;
    suggestedFocus: string[];
    efficiencyScore: number;
  } {
    const pathway = this.learningPathways.get(pathwayId);
    const history = this.progressHistory.get(pathwayId) || [];
    
    if (!pathway || history.length === 0) {
      return {
        learningVelocity: 0,
        strongAreas: [],
        challengingAreas: [],
        optimalStudyTime: 'morning',
        suggestedFocus: [],
        efficiencyScore: 0
      };
    }

    // Calculate learning velocity (progress per day)
    const learningVelocity = this.calculateLearningVelocity(history);
    
    // Identify strong and challenging areas
    const { strongAreas, challengingAreas } = this.identifyLearningAreas(pathway, history);
    
    // Analyze optimal study time
    const optimalStudyTime = this.analyzeOptimalStudyTime(history);
    
    // Suggest focus areas
    const suggestedFocus = this.suggestFocusAreas(pathway, challengingAreas);
    
    // Calculate efficiency score
    const efficiencyScore = this.calculateEfficiencyScore(pathway, history);

    return {
      learningVelocity,
      strongAreas,
      challengingAreas,
      optimalStudyTime,
      suggestedFocus,
      efficiencyScore
    };
  }

  // Private helper methods
  private generatePathwayId(userPreferences: UserPreferences): string {
    const timestamp = Date.now();
    const hash = btoa(`${userPreferences.level}-${userPreferences.goal}-${timestamp}`).slice(0, 8);
    return `pathway-${hash}`;
  }

  private buildSkillDependencyGraph(modules: Module[]): SkillDependency[] {
    const dependencies: SkillDependency[] = [];
    const allSkills = modules.flatMap(m => m.skillProgression || []);
    
    allSkills.forEach(skill => {
      dependencies.push({
        skillId: skill.skillId,
        dependsOn: skill.prerequisites,
        enablesSkills: skill.dependentSkills,
        masteryThreshold: 0.8,
        currentMastery: 0,
        isBlocked: false,
        blockingReasons: []
      });
    });
    
    return dependencies;
  }

  private extractLearningObjectives(modules: Module[]): LearningObjective[] {
    const objectives: LearningObjective[] = [];
    
    modules.forEach(module => {
      module.learningObjectives?.forEach((objective, index) => {
        objectives.push({
          objectiveId: `${module.id}-obj-${index}`,
          description: objective,
          moduleId: module.id,
          skillTargets: module.skillProgression?.map(sp => sp.skillId) || [],
          priority: index === 0 ? 'high' : 'medium',
          status: 'not-started',
          progressPercentage: 0,
          estimatedCompletionTime: module.duration || '30 minutes',
          alignmentScore: 0.9
        });
      });
    });
    
    return objectives;
  }

  private generateProgressMilestones(modules: Module[], skillDependencies: SkillDependency[]): ProgressMilestone[] {
    const milestones: ProgressMilestone[] = [];
    
    // Create milestones for every 25% of modules completed
    const milestonePoints = [0.25, 0.5, 0.75, 1.0];
    
    milestonePoints.forEach((point, index) => {
      const moduleIndex = Math.floor(modules.length * point) - 1;
      if (moduleIndex >= 0 && moduleIndex < modules.length) {
        const module = modules[moduleIndex];
        milestones.push({
          milestoneId: `milestone-${index + 1}`,
          title: `${Math.round(point * 100)}% Progress Milestone`,
          description: `Complete ${module.title} and achieve ${Math.round(point * 100)}% overall progress`,
          requiredSkills: module.skillProgression?.map(sp => sp.skillId) || [],
          requiredObjectives: [`${module.id}-obj-0`],
          isAchieved: false,
          nextMilestones: index < milestonePoints.length - 1 ? [`milestone-${index + 2}`] : [],
          rewardType: index === milestonePoints.length - 1 ? 'certificate' : 'badge'
        });
      }
    });
    
    return milestones;
  }

  private calculateEstimatedCompletion(modules: Module[], userPreferences: UserPreferences): string {
    const totalHours = modules.reduce((sum, module) => {
      const hours = parseInt(module.estimatedTime?.split(' ')[0] || '1');
      return sum + hours;
    }, 0);
    
    const dailyHours = userPreferences.timeCommitment === '30 minutes' ? 0.5 :
                      userPreferences.timeCommitment === '1 hour' ? 1 : 2;
    
    const daysToComplete = Math.ceil(totalHours / dailyHours);
    const completionDate = new Date();
    completionDate.setDate(completionDate.getDate() + daysToComplete);
    
    return completionDate.toISOString().split('T')[0];
  }

  private updateSkillMastery(pathway: LearningPathway, update: ProgressUpdate): string[] {
    const unlockedSkills: string[] = [];
    
    if (update.skillId) {
      const skillDep = pathway.skillDependencies.find(sd => sd.skillId === update.skillId);
      if (skillDep) {
        skillDep.currentMastery = Math.min(1, skillDep.currentMastery + (update.progressDelta / 100));
        
        // Check if this skill unlock others
        if (skillDep.currentMastery >= skillDep.masteryThreshold) {
          skillDep.enablesSkills.forEach(enabledSkillId => {
            const enabledSkill = pathway.skillDependencies.find(sd => sd.skillId === enabledSkillId);
            if (enabledSkill && enabledSkill.isBlocked) {
              const readiness = this.isSkillReady(pathway.pathwayId, enabledSkillId);
              if (readiness.isReady) {
                enabledSkill.isBlocked = false;
                enabledSkill.blockingReasons = [];
                unlockedSkills.push(enabledSkillId);
              }
            }
          });
        }
      }
    }
    
    return unlockedSkills;
  }

  private updateObjectiveProgress(pathway: LearningPathway, update: ProgressUpdate): void {
    if (update.objectiveId) {
      const objective = pathway.learningObjectives.find(obj => obj.objectiveId === update.objectiveId);
      if (objective) {
        objective.progressPercentage = Math.min(100, objective.progressPercentage + update.progressDelta);
        
        if (objective.progressPercentage >= 100) {
          objective.status = 'completed';
          objective.actualCompletionTime = update.timestamp;
        } else if (objective.progressPercentage > 0) {
          objective.status = 'in-progress';
        }
      }
    }
  }

  private checkMilestoneAchievements(pathway: LearningPathway): string[] {
    const achieved: string[] = [];
    
    pathway.progressMilestones.forEach(milestone => {
      if (!milestone.isAchieved) {
        const skillsAchieved = milestone.requiredSkills.every(skillId => {
          const skill = pathway.skillDependencies.find(sd => sd.skillId === skillId);
          return skill && skill.currentMastery >= skill.masteryThreshold;
        });
        
        const objectivesAchieved = milestone.requiredObjectives.every(objId => {
          const objective = pathway.learningObjectives.find(obj => obj.objectiveId === objId);
          return objective && objective.status === 'completed';
        });
        
        if (skillsAchieved && objectivesAchieved) {
          milestone.isAchieved = true;
          milestone.achievedAt = new Date().toISOString();
          achieved.push(milestone.milestoneId);
        }
      }
    });
    
    return achieved;
  }

  private calculateOverallProgress(pathway: LearningPathway): number {
    const totalObjectives = pathway.learningObjectives.length;
    if (totalObjectives === 0) return 0;
    
    const completedObjectives = pathway.learningObjectives.filter(obj => obj.status === 'completed').length;
    const inProgressObjectives = pathway.learningObjectives.filter(obj => obj.status === 'in-progress');
    
    let progressSum = completedObjectives * 100;
    progressSum += inProgressObjectives.reduce((sum, obj) => sum + obj.progressPercentage, 0);
    
    return Math.round(progressSum / totalObjectives);
  }

  private generateAdaptationRecommendations(pathway: LearningPathway): AdaptationRecommendation[] {
    const recommendations: AdaptationRecommendation[] = [];
    
    // Check for blocked skills
    pathway.skillDependencies.forEach(skill => {
      if (skill.isBlocked && skill.blockingReasons.length > 0) {
        recommendations.push({
          type: 'skill-reinforcement',
          target: skill.skillId,
          reason: `Skill blocked: ${skill.blockingReasons.join(', ')}`,
          suggestedAction: 'Review prerequisite skills and provide additional practice',
          priority: 'high',
          estimatedImpact: 0.8
        });
      }
    });
    
    // Check for slow progress
    const slowObjectives = pathway.learningObjectives.filter(obj => 
      obj.status === 'in-progress' && obj.progressPercentage < 25
    );
    
    if (slowObjectives.length > 2) {
      recommendations.push({
        type: 'difficulty-adjustment',
        target: 'overall-difficulty',
        reason: 'Multiple objectives showing slow progress',
        suggestedAction: 'Consider reducing difficulty or providing additional scaffolding',
        priority: 'medium',
        estimatedImpact: 0.6
      });
    }
    
    return recommendations;
  }

  private getSuggestedModules(pathway: LearningPathway, nextSkills: string[]): string[] {
    return pathway.modules.filter(moduleId => {
      const moduleObjectives = pathway.learningObjectives.filter(obj => obj.moduleId === moduleId);
      return moduleObjectives.some(obj => 
        obj.skillTargets.some(skillId => nextSkills.includes(skillId))
      );
    }).slice(0, 3);
  }

  private calculateLearningVelocity(history: ProgressUpdate[]): number {
    if (history.length < 2) return 0;
    
    const recentUpdates = history.slice(-10); // Last 10 updates
    const totalProgress = recentUpdates.reduce((sum, update) => sum + update.progressDelta, 0);
    const timeSpan = new Date(recentUpdates[recentUpdates.length - 1].timestamp).getTime() - 
                    new Date(recentUpdates[0].timestamp).getTime();
    const days = timeSpan / (1000 * 60 * 60 * 24);
    
    return days > 0 ? totalProgress / days : 0;
  }

  private identifyLearningAreas(pathway: LearningPathway, history: ProgressUpdate[]): {
    strongAreas: string[];
    challengingAreas: string[];
  } {
    const skillProgress = new Map<string, number[]>();
    
    history.forEach(update => {
      if (update.skillId) {
        if (!skillProgress.has(update.skillId)) {
          skillProgress.set(update.skillId, []);
        }
        skillProgress.get(update.skillId)!.push(update.progressDelta);
      }
    });
    
    const strongAreas: string[] = [];
    const challengingAreas: string[] = [];
    
    skillProgress.forEach((progress, skillId) => {
      const avgProgress = progress.reduce((sum, p) => sum + p, 0) / progress.length;
      if (avgProgress > 15) {
        strongAreas.push(skillId);
      } else if (avgProgress < 5) {
        challengingAreas.push(skillId);
      }
    });
    
    return { strongAreas, challengingAreas };
  }

  private analyzeOptimalStudyTime(history: ProgressUpdate[]): string {
    const timeSlots = { morning: 0, afternoon: 0, evening: 0 };
    
    history.forEach(update => {
      const hour = new Date(update.timestamp).getHours();
      if (hour < 12) timeSlots.morning += update.progressDelta;
      else if (hour < 18) timeSlots.afternoon += update.progressDelta;
      else timeSlots.evening += update.progressDelta;
    });
    
    return Object.entries(timeSlots).reduce((best, [time, progress]) => 
      progress > timeSlots[best as keyof typeof timeSlots] ? time : best, 'morning'
    );
  }

  private suggestFocusAreas(pathway: LearningPathway, challengingAreas: string[]): string[] {
    const suggestions = [...challengingAreas];
    
    // Add high-priority objectives that aren't progressing
    pathway.learningObjectives
      .filter(obj => obj.priority === 'high' && obj.progressPercentage < 25)
      .forEach(obj => {
        obj.skillTargets.forEach(skill => {
          if (!suggestions.includes(skill)) {
            suggestions.push(skill);
          }
        });
      });
    
    return suggestions.slice(0, 3);
  }

  private calculateEfficiencyScore(pathway: LearningPathway, history: ProgressUpdate[]): number {
    if (history.length === 0) return 0;
    
    const totalProgress = pathway.overallProgress;
    const timeSpent = history.length; // Simplified: number of study sessions
    const expectedProgress = timeSpent * 5; // Expected 5% progress per session
    
    return Math.min(1, totalProgress / expectedProgress);
  }

  private initializeAdaptationRules(): void {
    // Rule: Detect struggling learners
    this.adaptationRules.set('struggling-learner', (pathway) => {
      const recommendations: AdaptationRecommendation[] = [];
      const avgProgress = pathway.learningObjectives.reduce((sum, obj) => sum + obj.progressPercentage, 0) / pathway.learningObjectives.length;
      
      if (avgProgress < 30 && pathway.learningObjectives.length > 5) {
        recommendations.push({
          type: 'difficulty-adjustment',
          target: 'overall-pathway',
          reason: 'Low average progress across objectives',
          suggestedAction: 'Reduce complexity and add more scaffolding',
          priority: 'high',
          estimatedImpact: 0.7
        });
      }
      
      return recommendations;
    });
    
    // Rule: Detect fast learners
    this.adaptationRules.set('fast-learner', (pathway) => {
      const recommendations: AdaptationRecommendation[] = [];
      const completedObjectives = pathway.learningObjectives.filter(obj => obj.status === 'completed').length;
      const totalObjectives = pathway.learningObjectives.length;
      
      if (completedObjectives / totalObjectives > 0.8) {
        recommendations.push({
          type: 'content-modification',
          target: 'advanced-content',
          reason: 'High completion rate indicates readiness for advanced content',
          suggestedAction: 'Introduce advanced topics and challenges',
          priority: 'medium',
          estimatedImpact: 0.6
        });
      }
      
      return recommendations;
    });
  }
}

export const progressiveLearningTracker = new ProgressiveLearningTracker();
export type {
  SkillDependency,
  LearningObjective,
  ProgressMilestone,
  LearningPathway,
  ProgressUpdate,
  AdaptationRecommendation
};