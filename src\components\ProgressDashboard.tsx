import React, { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Award, 
  BookOpen, 
  Brain, 
  AlertCircle, 
  CheckCircle,
  Flame,
  Calendar,
  BarChart3,
  Lightbulb
} from 'lucide-react'
import { progressTracker } from '../services/progressTracker'
import type { PerformanceMetrics, LearningStreak, StudyGoal, LearningInsight } from '../services/progressTracker'

interface ProgressDashboardProps {
  moduleId?: string
  className?: string
}

const ProgressDashboard: React.FC<ProgressDashboardProps> = ({ moduleId, className = '' }) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [streak, setStreak] = useState<LearningStreak | null>(null)
  const [goals, setGoals] = useState<StudyGoal[]>([])
  const [insights, setInsights] = useState<LearningInsight[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'insights'>('overview')

  useEffect(() => {
    loadDashboardData()
  }, [moduleId])

  const loadDashboardData = () => {
    const performanceMetrics = progressTracker.getPerformanceMetrics(moduleId)
    const learningStreak = progressTracker.getStreak()
    const activeGoals = progressTracker.getActiveGoals()
    const learningInsights = progressTracker.getInsights()
    const studyStats = progressTracker.getStudyStatistics()

    setMetrics(performanceMetrics)
    setStreak(learningStreak)
    setGoals(activeGoals)
    setInsights(learningInsights)
    setStatistics(studyStats)
  }

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'achievement': return <Award className="w-4 h-4" />
      case 'suggestion': return <Lightbulb className="w-4 h-4" />
      case 'warning': return <AlertCircle className="w-4 h-4" />
      case 'milestone': return <CheckCircle className="w-4 h-4" />
      default: return <Brain className="w-4 h-4" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'achievement': return 'text-yellow-400 bg-yellow-900'
      case 'suggestion': return 'text-blue-400 bg-blue-900'
      case 'warning': return 'text-red-400 bg-red-900'
      case 'milestone': return 'text-green-400 bg-green-900'
      default: return 'text-gray-400 bg-gray-700'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500'
      case 'medium': return 'border-yellow-500'
      case 'low': return 'border-green-500'
      default: return 'border-gray-500'
    }
  }

  if (!metrics || !streak || !statistics) {
    return (
      <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-xl font-semibold text-red-400 mb-4">Learning Progress</h2>
        
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-700 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'goals', label: 'Goals', icon: Target },
            { id: 'insights', label: 'Insights', icon: Lightbulb }
          ].map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-red-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-600'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4 text-blue-400" />
                  <span className="text-sm text-gray-400">Study Time</span>
                </div>
                <div className="text-lg font-semibold text-gray-100">
                  {formatTime(metrics.totalStudyTime)}
                </div>
              </div>
              
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <BookOpen className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-gray-400">Lessons</span>
                </div>
                <div className="text-lg font-semibold text-gray-100">
                  {metrics.lessonsCompleted}
                </div>
              </div>
              
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-gray-400">Avg Score</span>
                </div>
                <div className="text-lg font-semibold text-gray-100">
                  {metrics.averageScore.toFixed(1)}%
                </div>
              </div>
              
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Flame className="w-4 h-4 text-red-400" />
                  <span className="text-sm text-gray-400">Streak</span>
                </div>
                <div className="text-lg font-semibold text-gray-100">
                  {streak.currentStreak} days
                </div>
              </div>
            </div>

            {/* Performance Bars */}
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-400">Accuracy Rate</span>
                  <span className="text-sm text-gray-300">{metrics.accuracyRate.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(metrics.accuracyRate, 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-400">Retention Rate</span>
                  <span className="text-sm text-gray-300">{metrics.retentionRate.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(metrics.retentionRate, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Areas Analysis */}
            <div className="grid md:grid-cols-2 gap-4">
              {metrics.strongAreas.length > 0 && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-green-400 mb-3 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Strong Areas
                  </h4>
                  <div className="space-y-1">
                    {metrics.strongAreas.slice(0, 3).map((area, index) => (
                      <div key={index} className="text-sm text-gray-300 bg-gray-600 px-2 py-1 rounded">
                        {area}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {metrics.weakAreas.length > 0 && (
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-red-400 mb-3 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    Areas for Improvement
                  </h4>
                  <div className="space-y-1">
                    {metrics.weakAreas.slice(0, 3).map((area, index) => (
                      <div key={index} className="text-sm text-gray-300 bg-gray-600 px-2 py-1 rounded">
                        {area}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Recommended Review */}
            {metrics.recommendedReview.length > 0 && (
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-yellow-400 mb-3 flex items-center gap-2">
                  <Brain className="w-4 h-4" />
                  Recommended for Review
                </h4>
                <div className="flex flex-wrap gap-2">
                  {metrics.recommendedReview.map((lesson, index) => (
                    <span key={index} className="text-xs bg-yellow-900 text-yellow-200 px-2 py-1 rounded">
                      {lesson}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'goals' && (
          <div className="space-y-4">
            {goals.length === 0 ? (
              <div className="text-center py-8">
                <Target className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">No active goals set</p>
                <p className="text-sm text-gray-500">Set learning goals to track your progress</p>
              </div>
            ) : (
              goals.map(goal => {
                const progress = (goal.current / goal.target) * 100
                const timeLeft = Math.ceil((goal.deadline.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
                
                return (
                  <div key={goal.id} className="bg-gray-700 p-4 rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-gray-100">{goal.description}</h4>
                        <p className="text-sm text-gray-400 capitalize">{goal.type} goal</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-300">
                          {goal.current} / {goal.target} min
                        </div>
                        <div className="text-xs text-gray-500">
                          {timeLeft > 0 ? `${timeLeft} days left` : 'Expired'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-600 rounded-full h-2 mb-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-500 ${
                          goal.achieved ? 'bg-green-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      ></div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-400">
                        {progress.toFixed(1)}% complete
                      </span>
                      {goal.achieved && (
                        <span className="text-xs bg-green-900 text-green-200 px-2 py-1 rounded">
                          Achieved!
                        </span>
                      )}
                    </div>
                  </div>
                )
              })
            )}
          </div>
        )}

        {activeTab === 'insights' && (
          <div className="space-y-3">
            {insights.length === 0 ? (
              <div className="text-center py-8">
                <Lightbulb className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">No insights available</p>
                <p className="text-sm text-gray-500">Complete more lessons to get personalized insights</p>
              </div>
            ) : (
              insights.map((insight, index) => (
                <div 
                  key={index} 
                  className={`bg-gray-700 p-4 rounded-lg border-l-4 ${getPriorityColor(insight.priority)}`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-full ${getInsightColor(insight.type)}`}>
                      {getInsightIcon(insight.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-100 mb-1">{insight.title}</h4>
                      <p className="text-sm text-gray-300 mb-2">{insight.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {insight.createdAt.toLocaleDateString()}
                        </span>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-2 py-1 rounded capitalize ${
                            insight.priority === 'high' ? 'bg-red-900 text-red-200' :
                            insight.priority === 'medium' ? 'bg-yellow-900 text-yellow-200' :
                            'bg-green-900 text-green-200'
                          }`}>
                            {insight.priority}
                          </span>
                          {insight.actionable && (
                            <span className="text-xs bg-blue-900 text-blue-200 px-2 py-1 rounded">
                              Actionable
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ProgressDashboard