import { Link } from 'react-router-dom'

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-red-900">
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
          <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent">
            日本語道
          </h1>
          <h2 className="text-2xl md:text-3xl font-light mb-8 text-gray-300">
            Master Japanese Through AI-Powered Learning Paths
          </h2>
          <p className="text-lg md:text-xl mb-12 text-gray-400 max-w-2xl mx-auto leading-relaxed">
            Embark on a personalized journey to fluency with our sophisticated AI that crafts learning roadmaps tailored to your goals and pace.
          </p>
          
          <Link 
            to="/generator" 
            className="inline-block px-12 py-4 bg-gradient-to-r from-red-700 to-red-600 hover:from-red-600 hover:to-red-500 text-white font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl border border-yellow-600 hover:border-yellow-500"
            style={{ clipPath: 'polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%)' }}
          >
            Begin Your Journey
          </Link>
        </div>
      </section>

      {/* Features Overview */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-6xl mx-auto px-6">
          <h3 className="text-4xl font-bold text-center mb-16 text-red-500">Features</h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-700 p-8 border-l-4 border-red-600 hover:bg-gray-600 transition-colors duration-300">
              <div className="text-3xl mb-4 text-yellow-500">🧠</div>
              <h4 className="text-xl font-semibold mb-4 text-red-400">AI-Powered Roadmaps</h4>
              <p className="text-gray-300 leading-relaxed">
                Our advanced AI analyzes your learning style and goals to create personalized study paths that adapt as you progress.
              </p>
            </div>
            
            <div className="bg-gray-700 p-8 border-l-4 border-red-600 hover:bg-gray-600 transition-colors duration-300">
              <div className="text-3xl mb-4 text-yellow-500">📚</div>
              <h4 className="text-xl font-semibold mb-4 text-red-400">Structured Modules</h4>
              <p className="text-gray-300 leading-relaxed">
                Learn through carefully crafted modules that build upon each other, ensuring solid foundation and steady progress.
              </p>
            </div>
            
            <div className="bg-gray-700 p-8 border-l-4 border-red-600 hover:bg-gray-600 transition-colors duration-300">
              <div className="text-3xl mb-4 text-yellow-500">📊</div>
              <h4 className="text-xl font-semibold mb-4 text-red-400">Progress Tracking</h4>
              <p className="text-gray-300 leading-relaxed">
                Monitor your advancement with detailed analytics and celebrate milestones on your path to fluency.
              </p>
            </div>
          </div>
        </div>
      </section>


    </div>
  )
}

export default LandingPage