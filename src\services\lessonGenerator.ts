import { z } from 'zod'
import type { Lesson, LessonContent } from '../stores/moduleStore'

// Zod schemas for lesson generation
const VocabularyItemSchema = z.object({
  term: z.string(),
  reading: z.string(),
  meaning: z.string()
})

const GrammarPatternSchema = z.object({
  pattern: z.string(),
  explanation: z.string(),
  examples: z.array(z.string())
})

const ExerciseSchema = z.object({
  question: z.string(),
  answer: z.string(),
  type: z.enum(['multiple-choice', 'fill-blank', 'translation', 'matching']).optional()
})

const LessonContentSchema = z.object({
  introduction: z.string(),
  mainContent: z.string(),
  examples: z.array(z.string()),
  exercises: z.array(ExerciseSchema),
  vocabulary: z.array(VocabularyItemSchema).optional(),
  grammar: z.array(GrammarPatternSchema).optional()
})

const GeneratedLessonSchema = z.object({
  id: z.string(),
  title: z.string(),
  type: z.enum(['reading', 'practice', 'quiz', 'video', 'conversation']),
  duration: z.string(),
  content: LessonContentSchema,
  difficulty: z.number().min(1).max(5),
  prerequisites: z.array(z.string()).optional()
})

const LessonGenerationResponseSchema = z.array(GeneratedLessonSchema)

export interface LessonGenerationOptions {
  moduleId: string
  userLevel: string
  focusAreas: string[]
  lessonCount: number
  startIndex: number
  previousLessons?: Lesson[]
  specificTopics?: string[]
  preferredTypes?: ('reading' | 'practice' | 'quiz' | 'video' | 'conversation')[]
}

export interface ModuleTemplate {
  id: string
  title: string
  description: string
  defaultLessonTypes: ('reading' | 'practice' | 'quiz' | 'video' | 'conversation')[]
  coreTopics: string[]
  progressionPath: string[]
  estimatedDuration: string
}

class LessonGeneratorService {
  private readonly API_ENDPOINT = 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
  private readonly API_KEY = 'your-api-key-here' // This should be configured via environment variables

  private moduleTemplates: { [key: string]: ModuleTemplate } = {
    'hiragana-katakana': {
      id: 'hiragana-katakana',
      title: 'Hiragana & Katakana Mastery',
      description: 'Learn to read and write the two basic Japanese syllabaries',
      defaultLessonTypes: ['reading', 'practice', 'quiz'],
      coreTopics: [
        'Vowels (あいうえお)',
        'K-line (かきくけこ)',
        'S-line (さしすせそ)',
        'T-line (たちつてと)',
        'N-line (なにぬねの)',
        'H-line (はひふへほ)',
        'M-line (まみむめも)',
        'Y-line (やゆよ)',
        'R-line (らりるれろ)',
        'W-line (わを)',
        'N (ん)',
        'Dakuten and Handakuten',
        'Katakana basics',
        'Long vowels and double consonants'
      ],
      progressionPath: [
        'Introduction to Japanese writing systems',
        'Basic vowel sounds',
        'Consonant-vowel combinations',
        'Special characters and modifications',
        'Reading practice',
        'Writing practice',
        'Recognition drills'
      ],
      estimatedDuration: '4-6 weeks'
    },
    'basic-grammar': {
      id: 'basic-grammar',
      title: 'Basic Grammar Structures',
      description: 'Essential grammar patterns for building simple sentences',
      defaultLessonTypes: ['reading', 'practice', 'conversation'],
      coreTopics: [
        'Sentence structure (SOV)',
        'Particles (は, を, が, に, で)',
        'Verb conjugations (present/past)',
        'Adjective types (i-adjectives, na-adjectives)',
        'Question formation',
        'Negation patterns',
        'Polite vs casual forms',
        'Numbers and counters',
        'Time expressions',
        'Location and direction'
      ],
      progressionPath: [
        'Basic sentence patterns',
        'Essential particles',
        'Verb basics',
        'Adjective usage',
        'Question and answer patterns',
        'Practical applications'
      ],
      estimatedDuration: '6-8 weeks'
    },
    'essential-vocabulary': {
      id: 'essential-vocabulary',
      title: 'Essential Vocabulary',
      description: 'Core vocabulary for everyday communication',
      defaultLessonTypes: ['reading', 'practice', 'quiz', 'conversation'],
      coreTopics: [
        'Greetings and basic expressions',
        'Family and relationships',
        'Numbers and time',
        'Food and drinks',
        'Colors and descriptions',
        'Body parts',
        'Clothing',
        'Transportation',
        'Weather',
        'Hobbies and activities'
      ],
      progressionPath: [
        'Daily essentials',
        'Personal information',
        'Describing things',
        'Activities and actions',
        'Practical vocabulary'
      ],
      estimatedDuration: '5-7 weeks'
    }
  }

  async generateLessons(options: LessonGenerationOptions): Promise<Lesson[]> {
    try {
      const template = this.moduleTemplates[options.moduleId]
      if (!template) {
        throw new Error(`No template found for module: ${options.moduleId}`)
      }

      const prompt = this.createLessonGenerationPrompt(options, template)
      const aiResponse = await this.callAI(prompt)
      const lessons = await this.parseAndValidateLessons(aiResponse, options)
      
      return lessons
    } catch (error) {
      console.error('Error generating lessons:', error)
      return this.generateFallbackLessons(options)
    }
  }

  private createLessonGenerationPrompt(options: LessonGenerationOptions, template: ModuleTemplate): string {
    const { moduleId, userLevel, focusAreas, lessonCount, startIndex, previousLessons, specificTopics } = options
    
    let prompt = `Generate ${lessonCount} progressive Japanese language lessons for the "${template.title}" module.

`
    
    prompt += `**Context:**
`
    prompt += `- Target Level: ${userLevel}
`
    prompt += `- Focus Areas: ${focusAreas.join(', ')}
`
    prompt += `- Starting from lesson ${startIndex + 1}
`
    prompt += `- Module Topics: ${template.coreTopics.join(', ')}

`
    
    if (previousLessons && previousLessons.length > 0) {
      prompt += `**Previous Lessons Completed:**
`
      previousLessons.forEach(lesson => {
        prompt += `- ${lesson.title} (${lesson.type})
`
      })
      prompt += `
`
    }
    
    if (specificTopics && specificTopics.length > 0) {
      prompt += `**Specific Topics to Cover:**
${specificTopics.join(', ')}

`
    }
    
    prompt += `**Requirements:**
`
    prompt += `1. Each lesson should build upon previous knowledge
`
    prompt += `2. Include varied lesson types: ${template.defaultLessonTypes.join(', ')}
`
    prompt += `3. Provide practical, engaging content
`
    prompt += `4. Include cultural context where relevant
`
    prompt += `5. Ensure appropriate difficulty progression

`
    
    prompt += `**For each lesson, provide:**
`
    prompt += `- Unique ID (format: ${moduleId}-lesson-${startIndex + 1}, etc.)
`
    prompt += `- Descriptive title
`
    prompt += `- Type (reading, practice, quiz, video, or conversation)
`
    prompt += `- Duration (realistic time estimate)
`
    prompt += `- Difficulty level (1-5 scale)
`
    prompt += `- Prerequisites (if any)
`
    prompt += `- Detailed content including:
`
    prompt += `  * Introduction (brief overview)
`
    prompt += `  * Main content (comprehensive explanation)
`
    prompt += `  * Examples (3-5 practical examples)
`
    prompt += `  * Exercises (3-4 varied practice activities)
`
    prompt += `  * Vocabulary (key terms with readings and meanings)
`
    prompt += `  * Grammar patterns (if applicable)

`
    
    prompt += `Return ONLY a valid JSON array with this exact structure:
`
    prompt += `[{
`
    prompt += `  "id": "string",
`
    prompt += `  "title": "string",
`
    prompt += `  "type": "reading|practice|quiz|video|conversation",
`
    prompt += `  "duration": "string",
`
    prompt += `  "content": {
`
    prompt += `    "introduction": "string",
`
    prompt += `    "mainContent": "string",
`
    prompt += `    "examples": ["string"],
`
    prompt += `    "exercises": [{"question": "string", "answer": "string", "type": "multiple-choice|fill-blank|translation|matching"}],
`
    prompt += `    "vocabulary": [{"term": "string", "reading": "string", "meaning": "string"}],
`
    prompt += `    "grammar": [{"pattern": "string", "explanation": "string", "examples": ["string"]}]
`
    prompt += `  },
`
    prompt += `  "difficulty": number,
`
    prompt += `  "prerequisites": ["string"]
`
    prompt += `}]`
    
    return prompt
  }

  private async callAI(prompt: string): Promise<string> {
    const response = await fetch(this.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.API_KEY}`
      },
      body: JSON.stringify({
        model: 'glm-4-plus',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 4000
      })
    })

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('No content received from AI')
    }

    return content
  }

  private async parseAndValidateLessons(aiResponse: string, options: LessonGenerationOptions): Promise<Lesson[]> {
    try {
      // Clean the response to extract JSON
      const jsonMatch = aiResponse.match(/\[[\s\S]*\]/)
      if (!jsonMatch) {
        throw new Error('No valid JSON array found in AI response')
      }

      const jsonString = jsonMatch[0]
      const rawLessons = JSON.parse(jsonString)
      
      // Validate with Zod schema
      const validatedLessons = LessonGenerationResponseSchema.parse(rawLessons)
      
      // Convert to Lesson objects
      const lessons: Lesson[] = validatedLessons.map((lessonData, index) => ({
        id: lessonData.id || `${options.moduleId}-lesson-${options.startIndex + index + 1}`,
        title: lessonData.title,
        type: lessonData.type,
        duration: lessonData.duration,
        completed: false,
        content: {
          introduction: lessonData.content.introduction || '',
          mainContent: lessonData.content.mainContent,
          examples: lessonData.content.examples || [],
          exercises: lessonData.content.exercises || [],
          vocabulary: lessonData.content.vocabulary || [],
          grammar: lessonData.content.grammar || []
        },
        difficulty: lessonData.difficulty,
        prerequisites: lessonData.prerequisites || [],
        aiGenerated: true,
        generatedAt: new Date()
      }))

      return lessons
    } catch (error) {
      console.error('Error parsing AI response:', error)
      console.error('Raw AI response:', aiResponse)
      throw new Error('Failed to parse AI-generated lessons')
    }
  }

  private generateFallbackLessons(options: LessonGenerationOptions): Lesson[] {
    const { moduleId, lessonCount, startIndex } = options
    const template = this.moduleTemplates[moduleId]
    const lessons: Lesson[] = []
    
    for (let i = 0; i < lessonCount; i++) {
      const lessonNumber = startIndex + i + 1
      const topicIndex = (startIndex + i) % template.coreTopics.length
      const topic = template.coreTopics[topicIndex]
      
      lessons.push({
        id: `${moduleId}-lesson-${lessonNumber}`,
        title: `Lesson ${lessonNumber}: ${topic}`,
        type: template.defaultLessonTypes[i % template.defaultLessonTypes.length],
        duration: '15 min',
        completed: false,
        content: {
          introduction: `Welcome to lesson ${lessonNumber}. In this lesson, we'll explore ${topic}.`,
          mainContent: `This lesson covers ${topic} as part of ${template.title}. You'll learn the fundamental concepts and practical applications of this important topic in Japanese language learning.`,
          examples: [
            `Example 1 demonstrating ${topic}`,
            `Example 2 showing practical usage`,
            `Example 3 with cultural context`
          ],
          exercises: [
            {
              question: `Practice exercise 1 for ${topic}`,
              answer: `Sample answer for exercise 1`
            },
            {
              question: `Practice exercise 2 for ${topic}`,
              answer: `Sample answer for exercise 2`
            }
          ],
          vocabulary: [
            {
              term: `Sample term ${lessonNumber}`,
              reading: `reading${lessonNumber}`,
              meaning: `Meaning related to ${topic}`
            }
          ]
        },
        difficulty: Math.min(Math.floor((startIndex + i) / 3) + 1, 5),
        prerequisites: lessonNumber > 1 ? [`${moduleId}-lesson-${lessonNumber - 1}`] : [],
        aiGenerated: false,
        generatedAt: new Date()
      })
    }
    
    return lessons
  }

  getModuleTemplate(moduleId: string): ModuleTemplate | null {
    return this.moduleTemplates[moduleId] || null
  }

  getAllModuleTemplates(): ModuleTemplate[] {
    return Object.values(this.moduleTemplates)
  }

  addModuleTemplate(template: ModuleTemplate): void {
    this.moduleTemplates[template.id] = template
  }
}

export const lessonGenerator = new LessonGeneratorService()
export default lessonGenerator