import { z } from 'zod';
import { dynamicLessonGenerator, type LessonGenerationOptions } from './dynamicLessonGenerator';
import { aiService } from './aiService';
import type { UserPreferences, LessonPlan, SkillProgression } from './aiService';
import type { RoadmapContext, ModuleContext } from './integrationBridgeService';

// Enhanced schemas for dynamic module generation
const ModuleGenerationRequestSchema = z.object({
  moduleId: z.string(),
  moduleTitle: z.string(),
  moduleDescription: z.string(),
  userPreferences: z.object({
    currentLevel: z.string(),
    learningGoal: z.string(),
    timeCommitment: z.string(),
    focusAreas: z.array(z.string()),
    learningStyle: z.string(),
    previousExperience: z.string()
  }),
  roadmapContext: z.object({
    totalModules: z.number(),
    modulePosition: z.number(),
    previousModules: z.array(z.string()),
    nextModules: z.array(z.string())
  }).optional()
});

const GeneratedModuleSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  duration: z.string(),
  lessons: z.number(),
  learningObjectives: z.array(z.string()),
  skillsToMaster: z.array(z.string()),
  culturalContext: z.array(z.string()).optional(),
  prerequisites: z.array(z.string()),
  difficulty: z.number().min(1).max(5),
  estimatedHours: z.number()
});

export interface ModuleGenerationRequest {
  moduleId: string;
  moduleTitle: string;
  moduleDescription: string;
  userPreferences: {
    currentLevel: string;
    learningGoal: string;
    timeCommitment: string;
    focusAreas: string[];
    learningStyle: string;
    previousExperience: string;
  };
  roadmapContext?: {
    totalModules: number;
    modulePosition: number;
    previousModules: string[];
    nextModules: string[];
  };
  targetSkills?: string[];
  prerequisites?: string[];
  difficultyLevel?: number;
  estimatedDuration?: string;
  moduleContext?: ModuleContext;
  additionalContext?: {
    roadmapPosition?: number;
    totalModules?: number;
    previousSkills?: string[];
    targetSkills?: string[];
  };
}

export interface GeneratedModule {
  id: string;
  title: string;
  description: string;
  duration: string;
  lessons: number;
  learningObjectives: string[];
  skillsToMaster: string[];
  culturalContext?: string[];
  prerequisites: string[];
  difficulty: number;
  estimatedHours: number;
  integrationMetadata?: {
    roadmapPosition?: number;
    connectedModules?: string[];
    skillProgression?: SkillProgression;
    adaptationLevel?: number;
  };
}

export interface ModuleWithLessons extends GeneratedModule {
  generatedLessons: any[]; // Will be populated with actual lessons
  isFullyGenerated: boolean;
  generatedAt: Date;
}

class DynamicModuleGeneratorService {
  private readonly API_ENDPOINT = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
  private readonly API_KEY = import.meta.env.VITE_AI_API_KEY || 'your-api-key-here';

  async generateModule(request: ModuleGenerationRequest): Promise<GeneratedModule> {
    try {
      // Validate input
      const validatedRequest = ModuleGenerationRequestSchema.parse(request);
      
      // Generate contextual module specification
      const prompt = this.buildModuleGenerationPrompt(validatedRequest);
      
      // Call AI service
      const aiResponse = await this.callAI(prompt);
      
      // Parse and validate response
      const module = await this.parseAndValidateModule(aiResponse, validatedRequest);
      
      return module;
    } catch (error) {
      console.error('Error generating module:', error);
      
      // Generate dynamic fallback instead of static template
      return this.generateDynamicFallback(request);
    }
  }

  async generateModuleWithLessons(request: ModuleGenerationRequest, lessonCount?: number): Promise<ModuleWithLessons> {
    try {
      // First generate the module specification
      const moduleSpec = await this.generateModule(request);
      
      // Then generate lessons for the module
      const actualLessonCount = lessonCount || moduleSpec.lessons;
      const lessonOptions: LessonGenerationOptions = {
        moduleId: moduleSpec.id,
        moduleTitle: moduleSpec.title,
        moduleDescription: moduleSpec.description,
        userLevel: request.userPreferences.currentLevel,
        userGoals: [request.userPreferences.learningGoal],
        focusAreas: request.userPreferences.focusAreas,
        learningStyle: request.userPreferences.learningStyle,
        lessonCount: actualLessonCount,
        startIndex: 0
      };
      
      const generatedLessons = await dynamicLessonGenerator.generateLessons(lessonOptions);
      
      return {
        ...moduleSpec,
        generatedLessons,
        isFullyGenerated: true,
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('Error generating module with lessons:', error);
      
      // Return module without lessons if lesson generation fails
      const moduleSpec = await this.generateModule(request);
      return {
        ...moduleSpec,
        generatedLessons: [],
        isFullyGenerated: false,
        generatedAt: new Date()
      };
    }
  }

  private buildModuleGenerationPrompt(request: ModuleGenerationRequest): string {
    const userAnalysis = this.analyzeUserProfile(request.userPreferences);
    const contextAnalysis = this.analyzeModuleContext(request);
    const requirementsSpec = this.buildRequirementsSpecification(request, userAnalysis);
    const roadmapIntegration = this.buildRoadmapIntegrationContext(request);
    
    return `You are an expert Japanese language curriculum designer specializing in adaptive, personalized learning modules with advanced roadmap integration capabilities. Generate a comprehensive module specification that seamlessly integrates with the broader learning pathway:

**User Profile Analysis:**
${userAnalysis}

**Module Context & Positioning:**
${contextAnalysis}

**Roadmap Integration Context:**
${roadmapIntegration}

**Detailed Requirements:**
${requirementsSpec}

**Advanced Customization Guidelines:**
1. Adapt module complexity to user's current proficiency and learning trajectory
2. Integrate seamlessly with user's overall learning roadmap
3. Build upon previous modules and prepare for future ones
4. Prioritize content based on stated goals and focus areas
5. Account for time constraints in module structure and pacing
6. Consider previous experience to optimize challenge level
7. Include cultural context relevant to user's interests and goals
8. Ensure proper scaffolding for skill development
9. Create clear skill progression pathways

**Module Specification Requirements:**
- Generate learning objectives that align with user goals and roadmap position
- Define specific skills to master within this module
- Estimate realistic completion time based on user's time commitment
- Determine appropriate lesson count for comprehensive coverage
- Specify cultural context elements when relevant
- Set difficulty level appropriate for user's current proficiency
- Calculate estimated study hours based on content depth
- Include integration metadata for roadmap connectivity

**Enhanced Response Format - Return ONLY valid JSON:**
{
  "id": "${request.moduleId}",
  "title": "Enhanced, contextual module title",
  "description": "Detailed description explaining module value and outcomes (2-3 sentences)",
  "duration": "X-Y weeks (realistic estimate)",
  "lessons": number (15-45 based on complexity),
  "learningObjectives": ["specific, measurable learning objectives"],
  "skillsToMaster": ["concrete skills user will develop"],
  "culturalContext": ["relevant cultural elements"],
  "prerequisites": ["required prior knowledge or modules"],
  "difficulty": number (1-5, appropriate for user level),
  "estimatedHours": number (total study time estimate),
  "integrationMetadata": {
    "roadmapPosition": ${request.additionalContext?.roadmapPosition || 1},
    "connectedModules": ["Previous and next module IDs"],
    "skillProgression": {
      "skillId": "primary-skill-id",
      "skillName": "Primary Skill Name",
      "category": "skill-category",
      "level": ${request.difficultyLevel || 1},
      "prerequisites": ["prerequisite-skills"],
      "dependentSkills": ["dependent-skills"],
      "masteryIndicators": ["mastery-indicator-1"],
      "practiceActivities": ["practice-activity-1"]
    },
    "adaptationLevel": number (1-5)
  }
}

Ensure the module specification is specifically tailored to this user's unique learning profile and provides maximum educational value within their learning journey with seamless roadmap integration.`;
  }

  private analyzeUserProfile(preferences: any): string {
    const proficiencyAssessment = this.assessDetailedProficiency(preferences);
    const learningVelocity = this.estimateLearningVelocity(preferences);
    const motivationFactors = this.identifyMotivationFactors(preferences);
    const challengeAreas = this.identifyChallengingAreas(preferences);
    
    return `- Current Level: ${preferences.currentLevel} (Detailed Assessment: ${proficiencyAssessment})
- Primary Learning Goal: ${preferences.learningGoal}
- Daily Time Commitment: ${preferences.timeCommitment}
- Focus Areas: ${preferences.focusAreas.join(', ')}
- Learning Style: ${preferences.learningStyle}
- Previous Experience: ${preferences.previousExperience}
- Estimated Learning Velocity: ${learningVelocity}
- Key Motivation Factors: ${motivationFactors.join(', ')}
- Potential Challenge Areas: ${challengeAreas.join(', ')}`;
  }

  private analyzeModuleContext(request: ModuleGenerationRequest): string {
    let context = `- Module ID: ${request.moduleId}
- Base Title: ${request.moduleTitle}
- Base Description: ${request.moduleDescription}`;
    
    if (request.roadmapContext) {
      context += `
- Position in Roadmap: Module ${request.roadmapContext.modulePosition} of ${request.roadmapContext.totalModules}
- Previous Modules: ${request.roadmapContext.previousModules.join(', ') || 'None'}
- Upcoming Modules: ${request.roadmapContext.nextModules.join(', ') || 'None'}`;
    }
    
    return context;
  }

  private buildRoadmapIntegrationContext(request: ModuleGenerationRequest): string {
    let context = '';
    
    if (request.roadmapContext) {
      context += `- Roadmap Position: Module ${request.roadmapContext.modulePosition} of ${request.roadmapContext.totalModules}\n`;
      context += `- Previous Modules: ${request.roadmapContext.previousModules.join(', ') || 'None'}\n`;
      context += `- Next Modules: ${request.roadmapContext.nextModules.join(', ') || 'None'}\n`;
    }
    
    if (request.additionalContext) {
      if (request.additionalContext.previousSkills) {
        context += `- Skills from Previous Modules: ${request.additionalContext.previousSkills.join(', ')}\n`;
      }
      if (request.additionalContext.targetSkills) {
        context += `- Target Skills for This Module: ${request.additionalContext.targetSkills.join(', ')}\n`;
      }
    }
    
    if (request.moduleContext) {
      context += `- Module Context: ${JSON.stringify(request.moduleContext)}\n`;
    }
    
    return context || 'No specific roadmap context provided';
  }

  private buildRequirementsSpecification(request: ModuleGenerationRequest, userAnalysis: string): string {
    let requirements = `- Customize content for ${request.userPreferences.currentLevel} level learner
- Align with learning goal: ${request.userPreferences.learningGoal}
- Optimize for ${request.userPreferences.timeCommitment} daily study sessions
- Emphasize focus areas: ${request.userPreferences.focusAreas.join(', ')}
- Adapt to ${request.userPreferences.learningStyle} learning style`;
    
    // Add context-specific requirements
    if (request.userPreferences.learningGoal.includes('travel')) {
      requirements += `
- Include practical travel scenarios and vocabulary
- Emphasize conversational skills for tourist situations`;
    }
    
    if (request.userPreferences.learningGoal.includes('business')) {
      requirements += `
- Include professional communication patterns
- Focus on business etiquette and formal language`;
    }
    
    if (request.userPreferences.focusAreas.includes('speaking')) {
      requirements += `
- Prioritize oral communication skills
- Include pronunciation and conversation practice`;
    }
    
    return requirements;
  }

  private assessDetailedProficiency(preferences: any): string {
    if (preferences.currentLevel === 'absolute-beginner' && preferences.previousExperience === 'none') {
      return 'true beginner with no prior exposure';
    }
    
    if (preferences.currentLevel === 'beginner' && preferences.previousExperience.includes('self-study')) {
      return 'motivated self-learner with basic foundation';
    }
    
    if (preferences.currentLevel === 'elementary' && preferences.focusAreas.length > 2) {
      return 'well-rounded learner ready for skill integration';
    }
    
    return `${preferences.currentLevel} with ${preferences.previousExperience} background`;
  }

  private estimateLearningVelocity(preferences: any): string {
    const timeScore = preferences.timeCommitment === '2+ hours' ? 3 :
                     preferences.timeCommitment === '1-2 hours' ? 2 :
                     preferences.timeCommitment === '30-60 minutes' ? 1 : 0;
    
    const experienceScore = preferences.previousExperience.includes('formal') ? 2 :
                           preferences.previousExperience.includes('self-study') ? 1 : 0;
    
    const totalScore = timeScore + experienceScore;
    
    if (totalScore >= 4) return 'accelerated';
    if (totalScore >= 2) return 'moderate';
    return 'steady';
  }

  private identifyMotivationFactors(preferences: any): string[] {
    const factors = [];
    
    if (preferences.learningGoal.includes('travel')) factors.push('practical-application');
    if (preferences.learningGoal.includes('business')) factors.push('professional-development');
    if (preferences.learningGoal.includes('culture')) factors.push('cultural-interest');
    if (preferences.learningGoal.includes('anime') || preferences.learningGoal.includes('manga')) factors.push('media-consumption');
    if (preferences.learningGoal.includes('academic')) factors.push('academic-achievement');
    
    return factors.length > 0 ? factors : ['general-interest'];
  }

  private identifyChallengingAreas(preferences: any): string[] {
    const challenges = [];
    
    if (!preferences.focusAreas.includes('speaking')) challenges.push('oral-communication');
    if (!preferences.focusAreas.includes('writing')) challenges.push('written-expression');
    if (!preferences.focusAreas.includes('listening')) challenges.push('audio-comprehension');
    if (preferences.currentLevel === 'absolute-beginner') challenges.push('script-recognition');
    if (preferences.timeCommitment === '15-30 minutes') challenges.push('time-constraints');
    
    return challenges;
  }

  private async callAI(prompt: string): Promise<string> {
    const response = await fetch(this.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.API_KEY}`
      },
      body: JSON.stringify({
        model: 'glm-4-plus',
        messages: [{
          role: 'user',
          content: prompt
        }],
        temperature: 0.7,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from AI');
    }

    return content;
  }

  private async parseAndValidateModule(aiResponse: string, request: ModuleGenerationRequest): Promise<GeneratedModule> {
    try {
      // Clean the response to extract JSON
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON object found in AI response');
      }

      const jsonString = jsonMatch[0];
      const rawModule = JSON.parse(jsonString);
      
      // Validate with Zod schema
      const validatedModule = GeneratedModuleSchema.parse(rawModule);
      
      return validatedModule;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      console.error('Raw AI response:', aiResponse);
      throw new Error('Failed to parse AI-generated module');
    }
  }

  private generateDynamicFallback(request: ModuleGenerationRequest): GeneratedModule {
    const userLevel = request.userPreferences.currentLevel;
    const focusAreas = request.userPreferences.focusAreas;
    const learningGoal = request.userPreferences.learningGoal;
    
    // Generate contextual fallback based on user profile
    const baseLessons = this.calculateBaseLessons(userLevel, request.userPreferences.timeCommitment);
    const difficulty = this.calculateDifficulty(userLevel);
    const estimatedHours = this.calculateEstimatedHours(baseLessons, difficulty);
    
    return {
      id: request.moduleId,
      title: this.generateContextualTitle(request.moduleTitle, learningGoal),
      description: this.generateContextualDescription(request.moduleDescription, focusAreas),
      duration: this.calculateDuration(baseLessons, request.userPreferences.timeCommitment),
      lessons: baseLessons,
      learningObjectives: this.generateContextualObjectives(request.moduleId, learningGoal),
      skillsToMaster: this.generateContextualSkills(request.moduleId, focusAreas),
      culturalContext: this.generateCulturalContext(learningGoal),
      prerequisites: this.determinePrerequisites(request.moduleId, userLevel),
      difficulty,
      estimatedHours,
      integrationMetadata: {
        roadmapPosition: request.additionalContext?.roadmapPosition || 1,
        connectedModules: this.generateConnectedModules(request),
        skillProgression: this.generateFallbackSkillProgression(request),
        adaptationLevel: this.calculateAdaptationLevel(request)
      }
    };
  }

  private generateConnectedModules(request: ModuleGenerationRequest): string[] {
    const connected = [];
    
    if (request.roadmapContext?.previousModules) {
      connected.push(...request.roadmapContext.previousModules);
    }
    
    if (request.roadmapContext?.nextModules) {
      connected.push(...request.roadmapContext.nextModules);
    }
    
    return connected;
  }

  private generateFallbackSkillProgression(request: ModuleGenerationRequest): any {
    const primarySkill = request.moduleId.split('-')[0] || 'japanese';
    
    return {
      skillId: `${primarySkill}-skill`,
      skillName: this.capitalizeFirst(primarySkill),
      category: 'language-fundamentals',
      level: this.calculateDifficulty(request.userPreferences.currentLevel),
      prerequisites: this.determinePrerequisites(request.moduleId, request.userPreferences.currentLevel),
      dependentSkills: [`${primarySkill}-advanced`],
      masteryIndicators: [`Complete ${primarySkill} exercises`],
      practiceActivities: [`${primarySkill} practice sessions`]
    };
  }

  private calculateAdaptationLevel(request: ModuleGenerationRequest): number {
    let level = 1;
    
    if (request.userPreferences.focusAreas.length > 2) level++;
    if (request.userPreferences.timeCommitment === '2+ hours') level++;
    if (request.userPreferences.previousExperience.includes('formal')) level++;
    if (request.roadmapContext?.modulePosition && request.roadmapContext.modulePosition > 1) level++;
    
    return Math.min(level, 5);
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private calculateBaseLessons(userLevel: string, timeCommitment: string): number {
    const baseLessons = userLevel === 'absolute-beginner' ? 25 :
                       userLevel === 'beginner' ? 22 :
                       userLevel === 'elementary' ? 20 : 18;
    
    const timeMultiplier = timeCommitment === '15-30 minutes' ? 1.2 :
                          timeCommitment === '2+ hours' ? 0.8 : 1.0;
    
    return Math.round(baseLessons * timeMultiplier);
  }

  private calculateDifficulty(userLevel: string): number {
    const difficultyMap: { [key: string]: number } = {
      'absolute-beginner': 1,
      'beginner': 2,
      'elementary': 3,
      'intermediate': 4,
      'advanced': 5
    };
    
    return difficultyMap[userLevel] || 2;
  }

  private calculateEstimatedHours(lessons: number, difficulty: number): number {
    const baseHoursPerLesson = 0.5 + (difficulty * 0.2);
    return Math.round(lessons * baseHoursPerLesson);
  }

  private generateContextualTitle(baseTitle: string, learningGoal: string): string {
    if (learningGoal.includes('travel')) {
      return `${baseTitle} for Travel`;
    }
    
    if (learningGoal.includes('business')) {
      return `${baseTitle} for Business`;
    }
    
    if (learningGoal.includes('culture')) {
      return `${baseTitle} & Cultural Context`;
    }
    
    return baseTitle;
  }

  private generateContextualDescription(baseDescription: string, focusAreas: string[]): string {
    let description = baseDescription;
    
    if (focusAreas.includes('speaking')) {
      description += ' with emphasis on speaking practice';
    }
    
    if (focusAreas.includes('reading')) {
      description += ' with focus on reading comprehension';
    }
    
    if (focusAreas.includes('writing')) {
      description += ' including writing skill development';
    }
    
    return description;
  }

  private generateContextualObjectives(moduleId: string, learningGoal: string): string[] {
    const objectives = [];
    
    // Base objectives from module type
    if (moduleId.includes('hiragana') || moduleId.includes('katakana')) {
      objectives.push('Master reading and writing of Japanese syllabaries');
      objectives.push('Develop character recognition fluency');
    } else if (moduleId.includes('grammar')) {
      objectives.push('Understand fundamental sentence structures');
      objectives.push('Apply grammar patterns in practical contexts');
    } else if (moduleId.includes('vocabulary')) {
      objectives.push('Acquire essential vocabulary for daily communication');
      objectives.push('Develop effective vocabulary retention strategies');
    }
    
    // Goal-specific objectives
    if (learningGoal.includes('travel')) {
      objectives.push('Apply knowledge in travel scenarios');
    }
    
    if (learningGoal.includes('business')) {
      objectives.push('Use appropriate language in professional contexts');
    }
    
    return objectives.length > 0 ? objectives : ['Develop foundational Japanese language skills'];
  }

  private generateContextualSkills(moduleId: string, focusAreas: string[]): string[] {
    const skills = [];
    
    // Module-specific skills
    if (moduleId.includes('hiragana')) {
      skills.push('Character recognition', 'Stroke order mastery', 'Reading fluency');
    } else if (moduleId.includes('grammar')) {
      skills.push('Sentence construction', 'Grammar pattern application', 'Syntax understanding');
    } else if (moduleId.includes('vocabulary')) {
      skills.push('Word association', 'Contextual usage', 'Memory techniques');
    }
    
    // Focus area skills
    if (focusAreas.includes('speaking')) {
      skills.push('Pronunciation accuracy', 'Conversational fluency');
    }
    
    if (focusAreas.includes('listening')) {
      skills.push('Audio comprehension', 'Sound recognition');
    }
    
    return skills.length > 0 ? skills : ['Basic Japanese communication skills'];
  }

  private generateCulturalContext(learningGoal: string): string[] {
    if (learningGoal.includes('travel')) {
      return ['Travel etiquette', 'Tourist interactions', 'Regional customs'];
    }
    
    if (learningGoal.includes('business')) {
      return ['Business etiquette', 'Professional communication', 'Workplace culture'];
    }
    
    if (learningGoal.includes('culture')) {
      return ['Traditional customs', 'Modern society', 'Cultural nuances'];
    }
    
    return ['General Japanese culture', 'Social customs', 'Language etiquette'];
  }

  private calculateDuration(lessons: number, timeCommitment: string): string {
    const dailyMinutes = timeCommitment === '15-30 minutes' ? 22.5 :
                        timeCommitment === '30-60 minutes' ? 45 :
                        timeCommitment === '1-2 hours' ? 90 : 150;
    
    const minutesPerLesson = 15; // Average lesson duration
    const totalMinutes = lessons * minutesPerLesson;
    const days = Math.ceil(totalMinutes / dailyMinutes);
    const weeks = Math.ceil(days / 7);
    
    return `${weeks}-${weeks + 1} weeks`;
  }

  private determinePrerequisites(moduleId: string, userLevel: string): string[] {
    if (userLevel === 'absolute-beginner') {
      return [];
    }
    
    // Basic prerequisite logic
    if (moduleId.includes('grammar') && !moduleId.includes('basic')) {
      return ['basic-grammar'];
    }
    
    if (moduleId.includes('kanji')) {
      return ['hiragana-katakana'];
    }
    
    if (moduleId.includes('conversation')) {
      return ['basic-grammar', 'essential-vocabulary'];
    }
    
    return [];
  }
}

export const dynamicModuleGenerator = new DynamicModuleGeneratorService();
export default dynamicModuleGenerator;