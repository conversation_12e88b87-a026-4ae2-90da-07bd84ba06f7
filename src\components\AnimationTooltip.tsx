import React from 'react'

interface AnimationTooltipProps {
  isVisible: boolean
  message: string
  position: { x: number; y: number }
}

const AnimationTooltip: React.FC<AnimationTooltipProps> = ({ isVisible, message, position }) => {
  if (!isVisible || !message) return null

  return (
    <div
      className="fixed z-[9999] pointer-events-none transition-all duration-300"
      style={{
        left: `${position.x}px`,
        top: `${position.y - 60}px`,
        transform: 'translateX(-50%)'
      }}
    >
      <div className="bg-gray-800 text-white px-3 py-2 rounded-lg shadow-xl border border-red-500/50 animate-in fade-in duration-300 backdrop-blur-sm">
        <div className="text-sm font-medium whitespace-nowrap">{message}</div>
        {/* Tooltip arrow */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
        </div>
      </div>
    </div>
  )
}

export default AnimationTooltip