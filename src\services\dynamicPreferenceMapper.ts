// Dynamic preference mapping service to replace hardcoded mappings

export interface UserPreferenceMapping {
  currentLevel: { [key: string]: string };
  learningGoal: { [key: string]: string };
  timeCommitment: { [key: string]: string };
  focusAreas: { [key: string]: string };
}

export interface DynamicPreferenceContext {
  userLevel: string;
  learningGoals: string[];
  culturalInterests: string[];
  timeConstraints: string;
  motivationFactors: string[];
}

class DynamicPreferenceMappingService {
  private baseMappings: UserPreferenceMapping = {
    currentLevel: {
      'absolute-beginner': 'Absolute Beginner',
      'beginner': 'Beginner', 
      'elementary': 'Elementary',
      'intermediate': 'Intermediate',
      'advanced': 'Advanced'
    },
    learningGoal: {
      'conversation': 'Conversational Fluency',
      'business': 'Business Japanese',
      'academic': 'Academic Study',
      'travel': 'Travel & Culture',
      'anime-manga': 'Anime & Manga',
      'culture': 'Cultural Understanding',
      'jlpt': 'JLPT Preparation',
      'hobby': 'Personal Interest'
    },
    timeCommitment: {
      '15-min': '15 minutes',
      '30-min': '30 minutes', 
      '1-hour': '1 hour',
      '2-hours': '2+ hours',
      'flexible': 'Flexible schedule',
      'intensive': 'Intensive study'
    },
    focusAreas: {
      'speaking': 'Speaking',
      'listening': 'Listening',
      'reading': 'Reading', 
      'writing': 'Writing',
      'kanji': 'Kanji',
      'grammar': 'Grammar',
      'vocabulary': 'Vocabulary',
      'pronunciation': 'Pronunciation',
      'culture': 'Cultural Context'
    }
  };

  /**
   * Dynamically map user preference values to display labels
   */
  mapPreferenceValue(category: keyof UserPreferenceMapping, value: string, context?: DynamicPreferenceContext): string {
    // First check base mappings
    const baseMapping = this.baseMappings[category][value];
    if (baseMapping) {
      return this.enhanceMapping(baseMapping, category, value, context);
    }

    // Generate contextual mapping for unknown values
    return this.generateContextualMapping(category, value, context);
  }

  /**
   * Map multiple preference values (e.g., focus areas array)
   */
  mapPreferenceArray(category: keyof UserPreferenceMapping, values: string[], context?: DynamicPreferenceContext): string[] {
    return values.map(value => this.mapPreferenceValue(category, value, context));
  }

  /**
   * Generate enhanced user profile display with contextual information
   */
  generateEnhancedProfile(userInfo: any, context?: DynamicPreferenceContext): {
    currentLevel: { label: string; description: string; icon: string };
    learningGoal: { label: string; description: string; icon: string };
    timeCommitment: { label: string; description: string; icon: string };
    focusAreas: Array<{ label: string; description: string; icon: string }>;
  } {
    return {
      currentLevel: this.generateEnhancedLevelInfo(userInfo.currentLevel, context),
      learningGoal: this.generateEnhancedGoalInfo(userInfo.learningGoal, context),
      timeCommitment: this.generateEnhancedTimeInfo(userInfo.timeCommitment, context),
      focusAreas: userInfo.focusAreas.map((area: string) => this.generateEnhancedFocusInfo(area, context))
    };
  }

  /**
   * Generate dynamic insights based on user preferences
   */
  generateDynamicInsights(userInfo: any, roadmapData: any): {
    learningVelocity: string;
    recommendedApproach: string;
    potentialChallenges: string[];
    strengthAreas: string[];
    customTips: string[];
  } {
    const velocity = this.calculateLearningVelocity(userInfo);
    const approach = this.recommendLearningApproach(userInfo);
    const challenges = this.identifyPotentialChallenges(userInfo);
    const strengths = this.identifyStrengthAreas(userInfo);
    const tips = this.generateCustomTips(userInfo, roadmapData);

    return {
      learningVelocity: velocity,
      recommendedApproach: approach,
      potentialChallenges: challenges,
      strengthAreas: strengths,
      customTips: tips
    };
  }

  private enhanceMapping(baseMapping: string, category: keyof UserPreferenceMapping, value: string, context?: DynamicPreferenceContext): string {
    if (!context) return baseMapping;

    // Add contextual enhancements based on user profile
    switch (category) {
      case 'learningGoal':
        if (value === 'travel' && context.timeConstraints === '15-min') {
          return `${baseMapping} (Quick Phrases)`;
        }
        if (value === 'business' && context.userLevel === 'advanced') {
          return `${baseMapping} (Professional Level)`;
        }
        break;
      
      case 'timeCommitment':
        if (value === '15-min' && context.motivationFactors.includes('busy-schedule')) {
          return `${baseMapping} (Micro-learning)`;
        }
        break;
    }

    return baseMapping;
  }

  private generateContextualMapping(category: string, value: string, context?: DynamicPreferenceContext): string {
    // Handle unknown values by generating contextual labels
    switch (category) {
      case 'currentLevel':
        return this.capitalizeWords(value.replace(/-/g, ' '));
      
      case 'learningGoal':
        if (value.includes('jlpt')) {
          return `JLPT ${value.toUpperCase()} Preparation`;
        }
        return this.capitalizeWords(value.replace(/-/g, ' '));
      
      case 'focusAreas':
        return this.capitalizeWords(value.replace(/-/g, ' '));
      
      default:
        return this.capitalizeWords(value.replace(/-/g, ' '));
    }
  }

  private generateEnhancedLevelInfo(level: string, context?: DynamicPreferenceContext): { label: string; description: string; icon: string } {
    const baseLabel = this.mapPreferenceValue('currentLevel', level, context);
    
    const descriptions: { [key: string]: string } = {
      'absolute-beginner': 'Starting from zero with no prior Japanese knowledge',
      'beginner': 'Basic understanding of hiragana/katakana and simple phrases',
      'elementary': 'Can form basic sentences and understand simple conversations',
      'intermediate': 'Comfortable with everyday conversations and basic grammar',
      'advanced': 'Fluent in most situations with complex grammar understanding'
    };

    const icons: { [key: string]: string } = {
      'absolute-beginner': '🌱',
      'beginner': '📚',
      'elementary': '🎯',
      'intermediate': '🚀',
      'advanced': '⭐'
    };

    return {
      label: baseLabel,
      description: descriptions[level] || 'Custom proficiency level',
      icon: icons[level] || '📖'
    };
  }

  private generateEnhancedGoalInfo(goal: string, context?: DynamicPreferenceContext): { label: string; description: string; icon: string } {
    const baseLabel = this.mapPreferenceValue('learningGoal', goal, context);
    
    const descriptions: { [key: string]: string } = {
      'conversation': 'Focus on speaking and listening for daily communication',
      'business': 'Professional Japanese for workplace and business contexts',
      'academic': 'Formal study approach with comprehensive grammar and writing',
      'travel': 'Practical phrases and cultural knowledge for visiting Japan',
      'anime-manga': 'Understanding Japanese media and pop culture references'
    };

    const icons: { [key: string]: string } = {
      'conversation': '💬',
      'business': '💼',
      'academic': '🎓',
      'travel': '✈️',
      'anime-manga': '🎌'
    };

    return {
      label: baseLabel,
      description: descriptions[goal] || 'Personalized learning objective',
      icon: icons[goal] || '🎯'
    };
  }

  private generateEnhancedTimeInfo(time: string, context?: DynamicPreferenceContext): { label: string; description: string; icon: string } {
    const baseLabel = this.mapPreferenceValue('timeCommitment', time, context);
    
    const descriptions: { [key: string]: string } = {
      '15-min': 'Quick daily sessions perfect for busy schedules',
      '30-min': 'Balanced approach with steady progress',
      '1-hour': 'Comprehensive daily study with good depth',
      '2-hours': 'Intensive learning for accelerated progress'
    };

    const icons: { [key: string]: string } = {
      '15-min': '⚡',
      '30-min': '⏰',
      '1-hour': '📅',
      '2-hours': '🔥'
    };

    return {
      label: baseLabel,
      description: descriptions[time] || 'Flexible study schedule',
      icon: icons[time] || '⏱️'
    };
  }

  private generateEnhancedFocusInfo(area: string, context?: DynamicPreferenceContext): { label: string; description: string; icon: string } {
    const baseLabel = this.mapPreferenceValue('focusAreas', area, context);
    
    const descriptions: { [key: string]: string } = {
      'speaking': 'Oral communication and pronunciation practice',
      'listening': 'Audio comprehension and natural speech understanding',
      'reading': 'Text comprehension and character recognition',
      'writing': 'Written expression and composition skills',
      'kanji': 'Chinese character learning and recognition',
      'grammar': 'Sentence structure and language patterns'
    };

    const icons: { [key: string]: string } = {
      'speaking': '🗣️',
      'listening': '👂',
      'reading': '📖',
      'writing': '✍️',
      'kanji': '漢',
      'grammar': '📝'
    };

    return {
      label: baseLabel,
      description: descriptions[area] || 'Specialized skill development',
      icon: icons[area] || '🎯'
    };
  }

  private calculateLearningVelocity(userInfo: any): string {
    let score = 0;
    
    // Time commitment factor
    const timeScores: { [key: string]: number } = {
      '15-min': 1, '30-min': 2, '1-hour': 3, '2-hours': 4
    };
    score += timeScores[userInfo.timeCommitment] || 2;
    
    // Level factor (higher level = faster potential progress)
    const levelScores: { [key: string]: number } = {
      'absolute-beginner': 1, 'beginner': 2, 'elementary': 3, 'intermediate': 4, 'advanced': 5
    };
    score += levelScores[userInfo.currentLevel] || 2;
    
    // Focus areas factor (more areas = potentially slower per area)
    score -= Math.max(0, userInfo.focusAreas.length - 3);
    
    if (score >= 7) return 'Accelerated (Fast-track learning)';
    if (score >= 5) return 'Moderate (Steady progress)';
    if (score >= 3) return 'Gradual (Consistent development)';
    return 'Relaxed (Comfortable pace)';
  }

  private recommendLearningApproach(userInfo: any): string {
    const hasMultipleFocus = userInfo.focusAreas.length > 2;
    const isIntensive = userInfo.timeCommitment === '2-hours';
    const isQuick = userInfo.timeCommitment === '15-min';
    
    if (isIntensive && hasMultipleFocus) {
      return 'Comprehensive immersion with skill integration';
    }
    
    if (isQuick) {
      return 'Micro-learning with focused skill development';
    }
    
    if (userInfo.learningGoal === 'travel') {
      return 'Practical scenario-based learning';
    }
    
    if (userInfo.learningGoal === 'business') {
      return 'Professional context with formal language patterns';
    }
    
    return 'Balanced skill development with practical application';
  }

  private identifyPotentialChallenges(userInfo: any): string[] {
    const challenges = [];
    
    if (userInfo.timeCommitment === '15-min') {
      challenges.push('Limited time for complex topics');
    }
    
    if (userInfo.focusAreas.length > 3) {
      challenges.push('Balancing multiple skill areas');
    }
    
    if (!userInfo.focusAreas.includes('speaking')) {
      challenges.push('Developing oral communication confidence');
    }
    
    if (userInfo.currentLevel === 'absolute-beginner') {
      challenges.push('Initial script learning curve');
    }
    
    if (userInfo.focusAreas.includes('kanji')) {
      challenges.push('Character memorization and retention');
    }
    
    return challenges.length > 0 ? challenges : ['Maintaining consistent practice'];
  }

  private identifyStrengthAreas(userInfo: any): string[] {
    const strengths = [];
    
    if (userInfo.timeCommitment === '2-hours') {
      strengths.push('Dedicated study time for deep learning');
    }
    
    if (userInfo.focusAreas.includes('speaking')) {
      strengths.push('Active communication practice');
    }
    
    if (userInfo.learningGoal === 'travel') {
      strengths.push('Clear practical motivation');
    }
    
    if (userInfo.currentLevel !== 'absolute-beginner') {
      strengths.push('Existing foundation to build upon');
    }
    
    return strengths.length > 0 ? strengths : ['Motivated to learn Japanese'];
  }

  private generateCustomTips(userInfo: any, roadmapData: any): string[] {
    const tips = [];
    
    if (userInfo.timeCommitment === '15-min') {
      tips.push('Use spaced repetition apps during commute time');
      tips.push('Focus on one skill per session for maximum efficiency');
    }
    
    if (userInfo.focusAreas.includes('speaking')) {
      tips.push('Practice speaking aloud even when studying alone');
      tips.push('Record yourself to track pronunciation improvement');
    }
    
    if (userInfo.learningGoal === 'travel') {
      tips.push('Learn phrases for specific situations you\'ll encounter');
      tips.push('Practice with travel scenario role-plays');
    }
    
    if (userInfo.focusAreas.includes('kanji')) {
      tips.push('Learn kanji in context with vocabulary, not isolation');
      tips.push('Use mnemonics and stories to remember character meanings');
    }
    
    // Add roadmap-specific tips
    if (roadmapData && roadmapData.modules) {
      const moduleCount = roadmapData.modules.length;
      if (moduleCount > 8) {
        tips.push('Break your roadmap into smaller milestones for motivation');
      }
    }
    
    return tips.length > 0 ? tips : ['Practice consistently, even if just for a few minutes daily'];
  }

  private capitalizeWords(str: string): string {
    return str.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }
}

export const dynamicPreferenceMapper = new DynamicPreferenceMappingService();
export default dynamicPreferenceMapper;