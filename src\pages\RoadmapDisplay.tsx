import { useEffect, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import type { Module, AIResponse } from '../services/aiService'
import { dynamicPreferenceMapper } from '../services/dynamicPreferenceMapper'
import { dynamicModuleGenerator } from '../services/dynamicModuleGenerator'
import { integrationBridgeService } from '../services/integrationBridgeService'
import { progressiveLearningTracker } from '../services/progressiveLearningTracker'

interface RoadmapData {
  modules: Module[]
  estimatedCompletionTime: string
  personalizedTips: string[]
}

interface EnhancedUserProfile {
  currentLevel: { label: string; description: string; icon: string }
  learningGoal: { label: string; description: string; icon: string }
  timeCommitment: { label: string; description: string; icon: string }
  focusAreas: Array<{ label: string; description: string; icon: string }>
}

interface DynamicInsights {
  learningVelocity: string
  recommendedApproach: string
  potentialChallenges: string[]
  strengthAreas: string[]
  customTips: string[]
}

const RoadmapDisplay = () => {
  const navigate = useNavigate()
  const [roadmapData, setRoadmapData] = useState<RoadmapData | null>(null)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [enhancedProfile, setEnhancedProfile] = useState<EnhancedUserProfile | null>(null)
  const [dynamicInsights, setDynamicInsights] = useState<DynamicInsights | null>(null)
  const [learningPathway, setLearningPathway] = useState<any>(null)
  const [progressData, setProgressData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [isGeneratingModules, setIsGeneratingModules] = useState(false)

  useEffect(() => {
    // Get user preferences and AI response from localStorage
    const userPreferences = localStorage.getItem('roadmapData')
    const aiResponse = localStorage.getItem('aiRoadmapResponse')
    const storedPathway = localStorage.getItem('learningPathway')
    
    if (!userPreferences || !aiResponse) {
      // If no data, redirect to generator
      navigate('/generator')
      return
    }
    
    try {
      const parsedUserInfo = JSON.parse(userPreferences)
      setUserInfo(parsedUserInfo)
      
      const parsedAIResponse: AIResponse = JSON.parse(aiResponse)
      const pathway = storedPathway ? JSON.parse(storedPathway) : null
      
      // Validate that the response has the required structure
      if (!parsedAIResponse.modules || !Array.isArray(parsedAIResponse.modules) || parsedAIResponse.modules.length === 0) {
        console.error('Invalid AI response structure: missing or empty modules array')
        navigate('/generator')
        return
      }
      
      // Ensure each module has required properties and proper types
       const validModules = parsedAIResponse.modules.filter(module => 
         module && 
         typeof module.id === 'string' && 
         typeof module.title === 'string' && 
         typeof module.description === 'string' &&
         typeof module.duration === 'string' &&
         typeof module.lessons === 'number' &&
         Array.isArray(module.dependsOn)
       ) as Module[]
       
       if (validModules.length === 0) {
         console.error('No valid modules found in AI response')
         navigate('/generator')
         return
       }
       
       const roadmapDataObj = {
         modules: validModules,
         estimatedCompletionTime: parsedAIResponse.estimatedCompletionTime || 'Not specified',
         personalizedTips: Array.isArray(parsedAIResponse.personalizedTips) ? parsedAIResponse.personalizedTips : ['Keep practicing consistently!']
       }
       
       setRoadmapData(roadmapDataObj)
       setLearningPathway(pathway)
       
       // Generate enhanced profile using dynamic preference mapper
       const enhanced = dynamicPreferenceMapper.generateEnhancedProfile(parsedUserInfo)
       setEnhancedProfile(enhanced)
       
       // Generate dynamic insights
       const insights = dynamicPreferenceMapper.generateDynamicInsights(parsedUserInfo, roadmapDataObj)
       setDynamicInsights(insights)
       
       // Initialize progress tracking if pathway exists
       if (pathway) {
         const initialProgress = progressiveLearningTracker.updateProgress(pathway.id, {
           moduleId: '',
           progressDelta: 0,
           timestamp: new Date().toISOString()
         })
         setProgressData(initialProgress)
       }
       
    } catch (error) {
      console.error('Failed to parse roadmap data:', error)
      navigate('/generator')
      return
    }
    
    setLoading(false)
  }, [navigate])

  // Function to handle on-demand module generation
  const handleGenerateModule = async (moduleId: string, moduleTitle: string, moduleDescription: string) => {
    if (!userInfo || !roadmapData || isGeneratingModules) return
    
    setIsGeneratingModules(true)
    
    try {
      // Find the current module in the roadmap
      const currentModule = roadmapData.modules.find(m => m.id === moduleId)
      if (!currentModule) {
        console.error('Module not found in roadmap')
        return
      }
      
      // Create roadmap context
      const roadmapContext = {
        roadmapId: `roadmap-${Date.now()}`,
        totalModules: roadmapData.modules.length,
        currentModuleIndex: roadmapData.modules.findIndex(m => m.id === moduleId),
        completedModules: [],
        skillProgression: [],
        learningObjectives: [],
        overallProgress: 0
      }
      
      // Create module context
      const moduleContext = {
        moduleId,
        title: currentModule.title,
        position: roadmapData.modules.findIndex(m => m.id === moduleId) + 1,
        prerequisites: currentModule.dependsOn || [],
        dependentModules: roadmapData.modules
          .filter(m => m.dependsOn?.includes(moduleId) || currentModule.dependsOn?.includes(m.id))
          .map(m => m.id),
        skillTargets: [],
        estimatedDuration: currentModule.duration || '1 week',
        difficultyLevel: 1
      }
      
      // Generate enhanced module using integration bridge
      const enhancedModule = await integrationBridgeService.generateContextualModule({
        moduleId,
        moduleTitle,
        moduleDescription,
        userPreferences: {
          currentLevel: userInfo.currentLevel,
          learningGoal: userInfo.learningGoal,
          timeCommitment: userInfo.timeCommitment,
          focusAreas: userInfo.focusAreas,
          learningStyle: userInfo.learningStyle || 'balanced',
          previousExperience: userInfo.previousExperience || 'none'
        },
        roadmapContext,
        moduleContext,
        skillContinuity: {
          previousSkills: [],
          targetSkills: [],
          skillGaps: []
        },
        objectiveAlignment: {
          roadmapObjectives: [],
          moduleObjectives: [],
          alignmentScore: 0.8
        }
      })
      
      // Store the generated module for later use
      const existingModules = JSON.parse(localStorage.getItem('generatedModules') || '{}')
      existingModules[moduleId] = enhancedModule
      localStorage.setItem('generatedModules', JSON.stringify(existingModules))
      
      // Update progress if pathway exists
      if (learningPathway && progressData) {
        const updatedProgress = progressiveLearningTracker.updateProgress(learningPathway.id, {
          moduleId,
          progressDelta: 10,
          timestamp: new Date().toISOString()
        })
        setProgressData(updatedProgress)
      }
      
      console.log('Generated enhanced module:', enhancedModule)
      
    } catch (error) {
      console.error('Failed to generate module:', error)
    } finally {
      setIsGeneratingModules(false)
    }
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-6xl mx-auto px-6 py-4 flex justify-between items-center">
          <Link to="/" className="text-red-400 hover:text-red-300 transition-colors">
            ← Back to Home
          </Link>
          <Link to="/generator" className="text-yellow-500 hover:text-yellow-400 transition-colors">
            Regenerate Roadmap
          </Link>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-12">
        <div className="mb-12">
          <h1 className="text-4xl font-bold mb-4 text-red-500">Your AI-Generated Learning Roadmap</h1>
          
          {userInfo && roadmapData && enhancedProfile && (
            <div className="space-y-6">
              <div className="bg-gray-800 p-6 rounded-lg border-l-4 border-yellow-600">
                <h2 className="text-xl font-semibold mb-4 text-yellow-500">Your Enhanced Learning Profile</h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="space-y-2">
                    <div className="text-gray-400 text-sm">Current Level</div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{enhancedProfile.currentLevel.icon}</span>
                      <div>
                        <div className="text-gray-100 font-medium">{enhancedProfile.currentLevel.label}</div>
                        <div className="text-gray-400 text-xs">{enhancedProfile.currentLevel.description}</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-gray-400 text-sm">Learning Goal</div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{enhancedProfile.learningGoal.icon}</span>
                      <div>
                        <div className="text-gray-100 font-medium">{enhancedProfile.learningGoal.label}</div>
                        <div className="text-gray-400 text-xs">{enhancedProfile.learningGoal.description}</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-gray-400 text-sm">Daily Time</div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{enhancedProfile.timeCommitment.icon}</span>
                      <div>
                        <div className="text-gray-100 font-medium">{enhancedProfile.timeCommitment.label}</div>
                        <div className="text-gray-400 text-xs">{enhancedProfile.timeCommitment.description}</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-gray-400 text-sm">Focus Areas</div>
                    <div className="flex flex-wrap gap-2">
                      {enhancedProfile.focusAreas.map((area, index) => (
                        <div key={index} className="flex items-center gap-1 px-2 py-1 bg-gray-700 text-xs rounded" title={area.description}>
                          <span>{area.icon}</span>
                          <span className="text-gray-100">{area.label}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Learning Pathway & Progress */}
              {learningPathway && (
                <div className="bg-gray-800 p-6 rounded-lg border-l-4 border-blue-600">
                  <h2 className="text-xl font-semibold mb-4 text-blue-400">Learning Pathway & Progress</h2>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <div className="text-gray-400 text-sm mb-2">Pathway ID</div>
                        <div className="text-gray-100 text-sm font-mono">{learningPathway.id}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm mb-2">Total Milestones</div>
                        <div className="text-gray-100 text-lg font-medium">{learningPathway.milestones?.length || 0}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm mb-2">Estimated Completion</div>
                        <div className="text-gray-100 text-lg font-medium">{learningPathway.estimatedCompletionTime || 'N/A'}</div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Skill Dependencies</div>
                      <div className="space-y-2">
                        {learningPathway.skillDependencies?.slice(0, 3).map((skill: any, index: number) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <span className="text-blue-400">→</span>
                            <span className="text-gray-100">{skill.skillId}</span>
                            <span className="text-gray-500 text-xs">({skill.level})</span>
                          </div>
                        )) || []}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Progress Overview</div>
                      {progressData ? (
                        <div className="space-y-2">
                          <div className="text-gray-100 text-sm">
                            <span className="font-medium">Overall Progress:</span> {Math.round(progressData.overallProgress * 100)}%
                          </div>
                          <div className="h-2 bg-gray-600 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-blue-500"
                              style={{ width: `${progressData.overallProgress * 100}%` }}
                            ></div>
                          </div>
                          <div className="text-gray-100 text-sm">
                            <span className="font-medium">Skills Mastered:</span> {progressData.skillMastery?.size || 0}
                          </div>
                        </div>
                      ) : (
                        <div className="text-gray-500 text-sm">No progress data available</div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              {/* Enhanced AI Insights */}
              {dynamicInsights && (
                <div className="bg-gray-800 p-6 rounded-lg border-l-4 border-red-600">
                  <h2 className="text-xl font-semibold mb-4 text-red-400">Enhanced AI Insights</h2>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <div className="text-gray-400 text-sm mb-2">Learning Velocity</div>
                        <div className="text-gray-100 text-lg font-medium">{dynamicInsights.learningVelocity}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm mb-2">Completion Time</div>
                        <div className="text-gray-100 text-lg font-medium">{roadmapData.estimatedCompletionTime}</div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Recommended Approach</div>
                      <div className="text-gray-100 text-sm">{dynamicInsights.recommendedApproach}</div>
                      
                      <div className="text-gray-400 text-sm mb-2 mt-4">Strength Areas</div>
                      <ul className="text-gray-100 text-sm space-y-1">
                        {dynamicInsights.strengthAreas.slice(0, 3).map((strength, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-green-400 mt-1">✓</span>
                            <span>{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <div className="text-gray-400 text-sm mb-2">Potential Challenges</div>
                      <ul className="text-gray-100 text-sm space-y-1 mb-4">
                        {dynamicInsights.potentialChallenges.slice(0, 3).map((challenge, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-yellow-400 mt-1">⚠</span>
                            <span>{challenge}</span>
                          </li>
                        ))}
                      </ul>
                      
                      <div className="text-gray-400 text-sm mb-2">Custom Tips</div>
                      <ul className="text-gray-100 text-sm space-y-1">
                        {dynamicInsights.customTips.slice(0, 3).map((tip, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-red-400 mt-1">💡</span>
                            <span>{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Roadmap Visualization */}
        {roadmapData && (
          <div className="space-y-6">
            {roadmapData.modules.map((module, index) => {
            // Calculate dependency lines
            const dependencyLines = []
            if (module.dependsOn.length > 0) {
              for (const depId of module.dependsOn) {
                const depIndex = roadmapData.modules.findIndex(m => m.id === depId)
                if (depIndex !== -1) {
                  dependencyLines.push(depIndex)
                }
              }
            }

            return (
              <div key={module.id} className="relative">
                {/* Module Card */}
                <div 
                  className={`p-6 border-l-4 rounded-lg transition-all duration-300 ${
                    module.status === 'completed' ? 'bg-gray-700 border-yellow-500' :
                    module.status === 'in-progress' ? 'bg-gray-700 border-red-500' :
                    module.status === 'available' ? 'bg-gray-800 border-green-500 hover:bg-gray-700' :
                    'bg-gray-800 border-gray-600 opacity-70'
                  }`}
                >
                  <div className="flex flex-wrap justify-between items-start gap-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold mb-2 flex items-center gap-2">
                        {module.status === 'locked' && (
                          <span className="text-gray-500">🔒</span>
                        )}
                        <span className={module.status === 'locked' ? 'text-gray-500' : 'text-gray-100'}>
                          {module.title}
                        </span>
                      </h3>
                      <p className={`mb-4 ${module.status === 'locked' ? 'text-gray-600' : 'text-gray-400'}`}>
                        {module.description}
                      </p>
                      
                      <div className="flex flex-wrap gap-4 text-sm mb-4">
                        <div className={module.status === 'locked' ? 'text-gray-600' : 'text-gray-400'}>
                          <span className="font-medium">Duration:</span> {module.duration}
                        </div>
                        <div className={module.status === 'locked' ? 'text-gray-600' : 'text-gray-400'}>
                          <span className="font-medium">Lessons:</span> {module.lessons}
                        </div>
                        {module.status !== 'locked' && (
                          <div className="text-gray-400">
                            <span className="font-medium">Status:</span> {
                              module.status === 'completed' ? 'Completed' :
                              module.status === 'in-progress' ? 'In Progress' :
                              'Available'
                            }
                          </div>
                        )}
                      </div>
                      
                      {/* Enhanced Module Information */}
                      {/* Check if module has enhanced features from localStorage */}
                      {(() => {
                        const storedModules = localStorage.getItem('generatedModules');
                        if (storedModules) {
                          try {
                            const modules = JSON.parse(storedModules);
                            const enhancedModule = modules[module.id];
                            if (enhancedModule && enhancedModule.integrationMetadata) {
                              return (
                                <div className="bg-gray-700 p-3 rounded-lg mb-4">
                                  <div className="text-gray-300 text-xs font-medium mb-2">Enhanced Features</div>
                                  <div className="grid grid-cols-2 gap-3 text-xs">
                                    <div>
                                      <span className="text-gray-400">Position:</span>
                                      <span className="text-gray-200 ml-1">{enhancedModule.integrationMetadata.roadmapPosition || 'N/A'}/{roadmapData.modules.length}</span>
                                    </div>
                                    <div>
                                      <span className="text-gray-400">Adaptation:</span>
                                      <span className="text-gray-200 ml-1">{enhancedModule.integrationMetadata.adaptationLevel || 'N/A'}</span>
                                    </div>
                                    {enhancedModule.integrationMetadata.connectedModules?.length > 0 && (
                                      <div className="col-span-2">
                                        <span className="text-gray-400">Connected:</span>
                                        <span className="text-gray-200 ml-1">{enhancedModule.integrationMetadata.connectedModules.length} modules</span>
                                      </div>
                                    )}
                                    {enhancedModule.integrationMetadata.skillProgression && (
                                      <div className="col-span-2">
                                        <span className="text-gray-400">Skills:</span>
                                        <span className="text-gray-200 ml-1">{enhancedModule.integrationMetadata.skillProgression.targetSkills?.length || 0} target skills</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            }
                          } catch (error) {
                            console.error('Error parsing stored modules:', error);
                          }
                        }
                        return null;
                      })()}
                    </div>
                    
                    <div className="w-full md:w-auto">
                      {module.status !== 'locked' && (
                        <div className="flex items-center gap-4">
                          {/* Progress Bar */}
                          {module.status !== 'available' && (
                            <div className="flex-1 md:w-32">
                              <div className="h-2 bg-gray-600 rounded-full overflow-hidden">
                                <div 
                                  className={`h-full ${module.status === 'completed' ? 'bg-yellow-500' : 'bg-red-500'}`}
                                  style={{ width: `${module.completionPercentage}%` }}
                                ></div>
                              </div>
                              <div className="text-xs text-right mt-1 text-gray-400">{module.completionPercentage}%</div>
                            </div>
                          )}
                          
                          {/* Action Buttons */}
                          <div className="flex items-center gap-2">
                            {/* Generate Module Button */}
                            <button
                              onClick={() => handleGenerateModule(module.id, module.title, module.description)}
                              disabled={isGeneratingModules}
                              className="px-3 py-2 text-xs font-medium bg-purple-600 hover:bg-purple-500 text-purple-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                              style={{ clipPath: 'polygon(6px 0%, 100% 0%, calc(100% - 6px) 100%, 0% 100%)' }}
                              title="Generate enhanced module content with AI"
                            >
                              {isGeneratingModules ? '⚡' : '🤖'} Generate
                            </button>
                            
                            {/* Main Action Button */}
                            <Link 
                              to={`/module/${module.id}`}
                              className={`px-4 py-2 text-sm font-medium transition-colors ${
                                module.status === 'completed' ? 'bg-yellow-600 hover:bg-yellow-500 text-yellow-100' :
                                module.status === 'in-progress' ? 'bg-red-600 hover:bg-red-500 text-red-100' :
                                'bg-green-600 hover:bg-green-500 text-green-100'
                              }`}
                              style={{ clipPath: 'polygon(6px 0%, 100% 0%, calc(100% - 6px) 100%, 0% 100%)' }}
                            >
                              {module.status === 'completed' ? 'Review' :
                               module.status === 'in-progress' ? 'Continue' :
                               'Start'}
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
          </div>
        )}
      </div>
    </div>
  )
}

export default RoadmapDisplay