import React, { useState, useEffect, useRef } from 'react'
import { Play, Pause, RotateCcw } from 'lucide-react'
import AnimatedCursor from './AnimatedCursor'
import ProgressAnimation from './ProgressAnimation'
import AnimationTooltip from './AnimationTooltip'

interface Module {
  id: string
  title: string
  lessons: number
  status: 'completed' | 'current' | 'upcoming'
  content: {
    description: string
    lessonTitles: string[]
  }
}

interface CursorPosition {
  x: number
  y: number
}

const AnimatedRoadmapMock: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [expandedModule, setExpandedModule] = useState<string | null>(null)
  const [cursorPosition, setCursorPosition] = useState<CursorPosition>({ x: 0, y: 0 })
  const [showCursor, setShowCursor] = useState(false)
  const [isClicking, setIsClicking] = useState(false)
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0)
  const [showProgressAnimation, setShowProgressAnimation] = useState(false)
  const [progressValue, setProgressValue] = useState(60)
  const [tooltipMessage, setTooltipMessage] = useState('')
  const [showTooltip, setShowTooltip] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<NodeJS.Timeout | null>(null)

  const modules: Module[] = [
    {
      id: 'hiragana',
      title: 'Hiragana & Katakana',
      status: 'completed',
      lessons: 15,
      content: {
        description: 'Master the fundamental Japanese writing systems',
        lessonTitles: ['Basic Hiragana', 'Hiragana Combinations', 'Katakana Basics', 'Foreign Words']
      }
    },
    {
      id: 'grammar',
      title: 'Basic Grammar',
      status: 'current',
      lessons: 24,
      content: {
        description: 'Learn essential grammar patterns and sentence structure',
        lessonTitles: ['Particles は・が・を', 'Verb Conjugation', 'Adjectives', 'Question Forms']
      }
    },
    {
      id: 'kanji',
      title: 'Kanji Fundamentals',
      status: 'upcoming',
      lessons: 32,
      content: {
        description: 'Build your kanji vocabulary systematically',
        lessonTitles: ['Basic Radicals', 'Common Kanji', 'Stroke Order', 'Readings']
      }
    }
  ]

  const animationSteps = [
    { type: 'moveCursor', target: 'grammar', duration: 2000, tooltip: 'Moving to Basic Grammar module...' },
    { type: 'hover', target: 'grammar', duration: 1000, tooltip: 'Hovering over module to preview' },
    { type: 'click', target: 'grammar', duration: 500, tooltip: 'Clicking to expand module' },
    { type: 'expand', target: 'grammar', duration: 1200, tooltip: 'Module expanding to show content' },
    { type: 'showContent', target: 'grammar', duration: 1500, tooltip: 'Displaying lesson content and structure' },
    { type: 'navigateLessons', target: 'grammar', duration: 4000, tooltip: 'Browsing through available lessons' },
    { type: 'showProgress', target: 'grammar', duration: 1500, tooltip: 'Updating progress and completion status' },
    { type: 'collapse', target: 'grammar', duration: 1000, tooltip: 'Returning to roadmap overview' },
    { type: 'complete', target: null, duration: 1000, tooltip: 'Animation complete! Try it yourself.' }
  ]

  const startAnimation = () => {
    setIsPlaying(true)
    setCurrentStep(0)
    setShowCursor(true)
    executeStep(0)
  }

  const pauseAnimation = () => {
    setIsPlaying(false)
    if (animationRef.current) {
      clearTimeout(animationRef.current)
    }
  }

  const resetAnimation = () => {
    setIsPlaying(false)
    setCurrentStep(0)
    setExpandedModule(null)
    setShowCursor(false)
    setIsClicking(false)
    setCurrentLessonIndex(0)
    setShowProgressAnimation(false)
    setProgressValue(60)
    setShowTooltip(false)
    setTooltipMessage('')
    if (animationRef.current) {
      clearTimeout(animationRef.current)
    }
  }

  const executeStep = (stepIndex: number) => {
    if (!isPlaying) return
    
    if (stepIndex >= animationSteps.length) {
      // Animation complete, auto-restart after delay
      animationRef.current = setTimeout(() => {
        resetAnimation()
        setTimeout(startAnimation, 1000)
      }, 2000)
      return
    }

    const step = animationSteps[stepIndex]
    setCurrentStep(stepIndex)
    
    // Show tooltip for current step
    if (step.tooltip) {
      setTooltipMessage(step.tooltip)
      setShowTooltip(true)
    }
    
    switch (step.type) {
      case 'moveCursor':
        moveCursorToModule(step.target!)
        break
      case 'hover':
        // Cursor is already positioned, just wait
        break
      case 'click':
        setIsClicking(true)
        setTimeout(() => setIsClicking(false), 300)
        break
      case 'expand':
        setExpandedModule(step.target!)
        break
      case 'showContent':
        // Content is already shown
        break
      case 'navigateLessons':
        // Simulate lesson navigation
        simulateLessonNavigation()
        break
      case 'showProgress':
        setShowProgressAnimation(true)
        setTimeout(() => {
          setProgressValue(75)
        }, 500)
        break
      case 'collapse':
        setExpandedModule(null)
        break
      case 'complete':
        setShowTooltip(false)
        break
    }

    // Schedule next step
    animationRef.current = setTimeout(() => {
      executeStep(stepIndex + 1)
    }, step.duration)
  }

  const moveCursorToModule = (moduleId: string) => {
    const moduleElement = document.getElementById(`module-${moduleId}`)
    if (moduleElement && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect()
      const moduleRect = moduleElement.getBoundingClientRect()
      
      const targetX = moduleRect.left - containerRect.left + moduleRect.width / 2
      const targetY = moduleRect.top - containerRect.top + moduleRect.height / 2
      
      setCursorPosition({ x: targetX, y: targetY })
    }
  }

  const simulateLessonNavigation = () => {
    let lessonIndex = 0
    const lessonInterval = setInterval(() => {
      setCurrentLessonIndex(lessonIndex)
      lessonIndex++
      if (lessonIndex >= 4) {
        clearInterval(lessonInterval)
        setCurrentLessonIndex(0)
      }
    }, 800)
  }

  useEffect(() => {
    // Auto-start animation on mount
    const timer = setTimeout(startAnimation, 1000)
    return () => {
      clearTimeout(timer)
      if (animationRef.current) {
        clearTimeout(animationRef.current)
      }
    }
  }, [])

  return (
    <div ref={containerRef} className="relative max-w-4xl mx-auto px-6">
      {/* Animation Controls */}
      <div className="flex justify-center mb-8 space-x-4">
        <button
          onClick={isPlaying ? pauseAnimation : startAnimation}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-colors duration-200"
        >
          {isPlaying ? <Pause size={16} /> : <Play size={16} />}
          <span>{isPlaying ? 'Pause' : 'Play'}</span>
        </button>
        <button
          onClick={resetAnimation}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors duration-200"
        >
          <RotateCcw size={16} />
          <span>Reset</span>
        </button>
      </div>

      {/* Animated Cursor */}
      <AnimatedCursor
        position={cursorPosition}
        isVisible={showCursor}
        isClicking={isClicking}
      />
      
      {/* Animation Tooltip */}
      <AnimationTooltip
        isVisible={showTooltip}
        message={tooltipMessage}
        position={cursorPosition}
      />

      {/* Roadmap Timeline */}
      <div className="relative">
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-red-600 to-yellow-600"></div>
        
        <div className="space-y-8">
          {modules.map((module, index) => (
            <div key={module.id} className="relative">
              <div
                id={`module-${module.id}`}
                className={`relative flex items-start transition-all duration-500 ${
                  expandedModule === module.id ? 'transform scale-105' : ''
                }`}
              >
                <div className={`w-4 h-4 rounded-full border-2 z-10 transition-all duration-300 ${
                  module.status === 'completed' ? 'bg-yellow-500 border-yellow-500 shadow-lg shadow-yellow-500/50' :
                  module.status === 'current' ? 'bg-red-500 border-red-500 shadow-lg shadow-red-500/50' :
                  'bg-gray-700 border-gray-600'
                }`}></div>
                
                <div className={`ml-8 p-6 rounded-lg border-l-4 transition-all duration-500 cursor-pointer ${
                  module.status === 'completed' ? 'bg-gray-700 border-yellow-500 hover:bg-gray-600' :
                  module.status === 'current' ? 'bg-gray-700 border-red-500 hover:bg-gray-600 hover:shadow-lg hover:shadow-red-500/20' :
                  'bg-gray-800 border-gray-600 hover:bg-gray-700'
                } ${
                  expandedModule === module.id ? 'shadow-2xl shadow-red-500/30' : ''
                }`}>
                  <h4 className="text-xl font-semibold mb-2 text-gray-100">{module.title}</h4>
                  <p className="text-gray-400">{module.lessons} lessons</p>
                  <div className={`inline-block px-3 py-1 rounded text-sm mt-2 ${
                    module.status === 'completed' ? 'bg-yellow-600 text-yellow-100' :
                    module.status === 'current' ? 'bg-red-600 text-red-100' :
                    'bg-gray-600 text-gray-300'
                  }`}>
                    {module.status === 'completed' ? 'Completed' :
                     module.status === 'current' ? 'In Progress' : 'Upcoming'}
                  </div>
                  
                  {/* Expanded Content */}
                  {expandedModule === module.id && (
                    <div className="mt-6 pt-6 border-t border-gray-600 animate-in slide-in-from-top duration-500">
                      <p className="text-gray-300 mb-4">{module.content.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {module.content.lessonTitles.map((lesson, lessonIndex) => (
                          <div
                            key={lessonIndex}
                            className={`p-3 rounded border transition-all duration-300 ${
                              currentLessonIndex === lessonIndex && expandedModule === module.id
                                ? 'bg-red-900 border-red-500 shadow-lg shadow-red-500/30'
                                : 'bg-gray-800 border-gray-600 hover:border-red-500'
                            } ${
                              lessonIndex === 0 ? 'animate-in fade-in duration-700' :
                              lessonIndex === 1 ? 'animate-in fade-in duration-700 animation-delay-200' :
                              lessonIndex === 2 ? 'animate-in fade-in duration-700 animation-delay-400' :
                              'animate-in fade-in duration-700 animation-delay-600'
                            }`}
                          >
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                                currentLessonIndex === lessonIndex && expandedModule === module.id
                                  ? 'bg-yellow-500'
                                  : 'bg-red-500'
                              }`}></div>
                              <span className="text-sm text-gray-200">{lesson}</span>
                              {currentLessonIndex === lessonIndex && expandedModule === module.id && (
                                <div className="ml-auto">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div className="mt-6 pt-4 border-t border-gray-600">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-300">Module Progress</span>
                          <span className="text-xs text-gray-500">Lesson {currentLessonIndex + 1} of {module.content.lessonTitles.length}</span>
                        </div>
                        
                        {expandedModule === module.id && (
                          <ProgressAnimation
                            isVisible={showProgressAnimation}
                            currentProgress={60}
                            targetProgress={progressValue}
                            duration={1500}
                          />
                        )}
                        
                        {!showProgressAnimation && (
                          <div className="flex items-center space-x-3">
                            <div className="flex-1 bg-gray-700 rounded-full h-3">
                              <div className="bg-gradient-to-r from-red-500 to-yellow-500 h-3 rounded-full transition-all duration-1000" 
                                   style={{ width: module.status === 'current' ? '60%' : module.status === 'completed' ? '100%' : '0%' }}></div>
                            </div>
                            <span className="text-sm text-gray-300 min-w-[40px]">
                              {module.status === 'current' ? '60%' : module.status === 'completed' ? '100%' : '0%'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default AnimatedRoadmapMock