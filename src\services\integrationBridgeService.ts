import { z } from 'zod';
import { aiService } from './aiService';
import { dynamicModuleGenerator } from './dynamicModuleGenerator';
import type { UserPreferences, Module, AIResponse, LessonPlan, SkillProgression } from './aiService';
import type { ModuleGenerationRequest, GeneratedModule } from './dynamicModuleGenerator';

// Enhanced context schemas for integration
const RoadmapContextSchema = z.object({
  roadmapId: z.string(),
  totalModules: z.number(),
  currentModuleIndex: z.number(),
  completedModules: z.array(z.string()),
  skillProgression: z.array(z.object({
    skillId: z.string(),
    currentLevel: z.number(),
    targetLevel: z.number(),
    masteryPercentage: z.number()
  })),
  learningObjectives: z.array(z.string()),
  overallProgress: z.number()
});

const ModuleContextSchema = z.object({
  moduleId: z.string(),
  title: z.string(),
  position: z.number(),
  prerequisites: z.array(z.string()),
  dependentModules: z.array(z.string()),
  skillTargets: z.array(z.string()),
  estimatedDuration: z.string(),
  difficultyLevel: z.number()
});

const IntegrationMetadataSchema = z.object({
  generatedAt: z.string(),
  version: z.string(),
  contextHash: z.string(),
  roadmapPosition: z.number().optional(),
  connectedModules: z.array(z.string()).optional(),
  skillProgression: z.object({
    skillId: z.string(),
    skillName: z.string(),
    category: z.string(),
    level: z.number(),
    prerequisites: z.array(z.string()),
    dependentSkills: z.array(z.string()),
    masteryIndicators: z.array(z.string()),
    practiceActivities: z.array(z.string())
  }).optional(),
  adaptationLevel: z.number().optional()
});

type RoadmapContext = z.infer<typeof RoadmapContextSchema>;
type ModuleContext = z.infer<typeof ModuleContextSchema>;
type IntegrationMetadata = z.infer<typeof IntegrationMetadataSchema>;

interface EnhancedModuleRequest extends ModuleGenerationRequest {
  moduleTitle: string;
  moduleDescription: string;
  roadmapContext: RoadmapContext;
  moduleContext: ModuleContext;
  skillContinuity: {
    previousSkills: string[];
    targetSkills: string[];
    skillGaps: string[];
  };
  objectiveAlignment: {
    roadmapObjectives: string[];
    moduleObjectives: string[];
    alignmentScore: number;
  };
  roadmapContextCompat: {
    totalModules: number;
    modulePosition: number;
    previousModules: string[];
    nextModules: string[];
  };
}

interface EnhancedGeneratedModule extends GeneratedModule {
  integrationMetadata: IntegrationMetadata;
  skillProgression?: SkillProgression[];
  skillContinuity: {
    skillProgression: SkillProgression[];
    prerequisiteVerification: boolean;
    dependencyMapping: { [key: string]: string[] };
  };
  contextualEnhancements: {
    roadmapAwareness: boolean;
    adaptiveContent: boolean;
    progressiveComplexity: boolean;
  };
  status?: 'available' | 'in-progress' | 'completed' | 'locked';
  completionPercentage?: number;
}

class IntegrationBridgeService {
  private moduleGenerator = dynamicModuleGenerator;
  private consistencyCache: Map<string, any> = new Map();

  constructor() {
    // Module generator is already initialized as imported instance
  }

  /**
   * Generate an enhanced roadmap with detailed integration metadata
   */
  async generateEnhancedRoadmap(userPreferences: UserPreferences): Promise<AIResponse> {
    try {
      // Generate base roadmap
      const roadmap = await aiService.generateRoadmap(userPreferences);
      
      // Enhance with integration metadata
      const enhancedRoadmap = await this.enhanceRoadmapWithIntegration(roadmap, userPreferences);
      
      // Verify data consistency
      const consistencyCheck = this.verifyDataConsistency(enhancedRoadmap);
      
      return {
        ...enhancedRoadmap,
        integrationMetadata: {
          generatedAt: new Date().toISOString(),
          version: '2.0.0',
          contextHash: this.generateContextHash(userPreferences),
          roadmapPosition: 1,
          connectedModules: [],
          adaptationLevel: 1
        }
      };
    } catch (error) {
      console.error('Error generating enhanced roadmap:', error);
      throw error;
    }
  }

  /**
   * Generate a contextual module with roadmap integration
   */
  async generateContextualModule(request: EnhancedModuleRequest): Promise<EnhancedGeneratedModule> {
    try {
      // Validate skill continuity
      const skillContinuityCheck = this.validateSkillContinuity(request);
      
      // Enhance module request with roadmap context
      const enhancedRequest = this.enhanceModuleRequest(request);
      
      // Generate module with enhanced context
      const module = await this.moduleGenerator.generateModuleWithLessons(enhancedRequest);
      
      // Apply contextual enhancements
      const enhancedModule = this.applyContextualEnhancements(module, request);
      
      return {
        ...enhancedModule,
        integrationMetadata: {
          generatedAt: new Date().toISOString(),
          version: '2.0.0',
          contextHash: this.generateModuleContextHash(request),
          dataConsistencyCheck: true,
          skillContinuityVerified: skillContinuityCheck.isValid,
          objectiveAlignment: this.verifyObjectiveAlignment(request)
        },
        skillContinuity: {
          skillProgression: this.generateSkillProgression(request),
          prerequisiteVerification: skillContinuityCheck.prerequisitesMet,
          dependencyMapping: this.mapSkillDependencies(request)
        },
        contextualEnhancements: {
          roadmapAwareness: true,
          adaptiveContent: this.shouldAdaptContent(request),
          progressiveComplexity: this.calculateProgressiveComplexity(request)
        }
      };
    } catch (error) {
      console.error('Error generating contextual module:', error);
      throw error;
    }
  }

  /**
   * Validate skill continuity between modules
   */
  private validateSkillContinuity(request: EnhancedModuleRequest): {
    isValid: boolean;
    prerequisitesMet: boolean;
    skillGaps: string[];
    recommendations: string[];
  } {
    const { skillContinuity, moduleContext } = request;
    
    // Check if previous skills meet prerequisites
    const prerequisitesMet = moduleContext.prerequisites.every(prereq => 
      skillContinuity.previousSkills.includes(prereq)
    );
    
    // Identify skill gaps
    const skillGaps = moduleContext.prerequisites.filter(prereq => 
      !skillContinuity.previousSkills.includes(prereq)
    );
    
    // Generate recommendations for skill gaps
    const recommendations = skillGaps.map(gap => 
      `Consider reviewing ${gap} before proceeding with this module`
    );
    
    return {
      isValid: prerequisitesMet && skillGaps.length === 0,
      prerequisitesMet,
      skillGaps,
      recommendations
    };
  }

  /**
   * Enhance roadmap with integration metadata
   */
  private async enhanceRoadmapWithIntegration(roadmap: AIResponse, userPreferences: UserPreferences): Promise<AIResponse> {
    const enhancedModules = roadmap.modules.map((module, index) => {
      const roadmapContext = this.createRoadmapContext(roadmap, index);
      const moduleContext = this.createModuleContext(module, index, roadmap.modules);
      
      return {
        ...module,
        status: 'available' as const,
        completionPercentage: 0,
        contentContext: {
          ...module.contentContext,
          roadmapPosition: index + 1,
          totalModules: roadmap.modules.length,
          prerequisiteModules: roadmap.modules.slice(0, index).map(m => m.id),
          dependentModules: roadmap.modules.slice(index + 1).map(m => m.id),
          skillContinuityMap: this.generateSkillContinuityMap(module, roadmap.modules, index)
        },
        assessmentStrategy: {
          ...module.assessmentStrategy,
          progressiveAssessment: true,
          skillTrackingEnabled: true,
          adaptiveQuestions: this.generateAdaptiveQuestions(module, userPreferences)
        }
      };
    });

    return {
      ...roadmap,
      modules: enhancedModules,
      overallSkillProgression: this.generateOverallSkillProgression(enhancedModules),
      learningPathway: {
        ...roadmap.learningPathway,
        milestones: this.generateProgressionMilestones(enhancedModules),
        checkpoints: [],
        adaptationPoints: []
      }
    };
  }

  /**
   * Create roadmap context for a specific module
   */
  private createRoadmapContext(roadmap: AIResponse, moduleIndex: number): RoadmapContext {
    return {
      roadmapId: `roadmap-${Date.now()}`,
      totalModules: roadmap.modules.length,
      currentModuleIndex: moduleIndex,
      completedModules: roadmap.modules.slice(0, moduleIndex).map(m => m.id),
      skillProgression: this.extractSkillProgression(roadmap.modules, moduleIndex),
      learningObjectives: roadmap.modules[moduleIndex]?.learningObjectives || [],
      overallProgress: (moduleIndex / roadmap.modules.length) * 100
    };
  }

  /**
   * Create module context for integration
   */
  private createModuleContext(module: Module, index: number, allModules: Module[]): ModuleContext {
    return {
      moduleId: module.id,
      title: module.title,
      position: index + 1,
      prerequisites: allModules.slice(0, index).map(m => m.id),
      dependentModules: allModules.slice(index + 1).map(m => m.id),
      skillTargets: module.skillProgression?.map(sp => sp.skillId) || [],
      estimatedDuration: module.duration || '30 minutes',
      difficultyLevel: 1
    };
  }

  /**
   * Generate context hash for caching and consistency
   */
  private generateContextHash(userPreferences: UserPreferences): string {
    const contextString = `${userPreferences.currentLevel}-${userPreferences.learningGoal}-${userPreferences.timeCommitment}-${userPreferences.focusAreas?.join(',') || ''}`;
    return btoa(contextString).slice(0, 16);
  }

  /**
   * Generate module-specific context hash
   */
  private generateModuleContextHash(request: EnhancedModuleRequest): string {
    const contextString = `${request.moduleContext.moduleId}-${request.roadmapContext.roadmapId}-${request.userPreferences.currentLevel}`;
    return btoa(contextString).slice(0, 16);
  }

  /**
   * Verify data consistency across the roadmap
   */
  private verifyDataConsistency(roadmap: AIResponse): {
    isValid: boolean;
    skillContinuity: boolean;
    objectiveAlignment: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    
    // Check skill continuity
    let skillContinuity = true;
    for (let i = 1; i < roadmap.modules.length; i++) {
      const currentModule = roadmap.modules[i];
      const previousModules = roadmap.modules.slice(0, i);
      
      // Verify prerequisites are met
      const prerequisitesMet = currentModule.skillProgression?.every(skill => 
        skill.prerequisites.every(prereq => 
          previousModules.some(pm => 
            pm.skillProgression?.some(ps => ps.skillId === prereq)
          )
        )
      ) ?? true;
      
      if (!prerequisitesMet) {
        skillContinuity = false;
        issues.push(`Module ${currentModule.id} has unmet prerequisites`);
      }
    }
    
    // Check objective alignment
    const objectiveAlignment = roadmap.modules.every(module => 
      module.learningObjectives && module.learningObjectives.length > 0
    );
    
    if (!objectiveAlignment) {
      issues.push('Some modules lack proper learning objectives');
    }
    
    return {
      isValid: issues.length === 0,
      skillContinuity,
      objectiveAlignment,
      issues
    };
  }

  // Helper methods for enhanced functionality
  private enhanceModuleRequest(request: EnhancedModuleRequest): ModuleGenerationRequest {
    return {
      moduleId: request.moduleId,
      userPreferences: request.userPreferences,
      focusAreas: request.userPreferences.focusAreas,
      additionalContext: {
        roadmapPosition: request.moduleContext.position,
        totalModules: request.roadmapContext.totalModules,
        previousSkills: request.skillContinuity.previousSkills,
        targetSkills: request.skillContinuity.targetSkills
      },
      roadmapContext: {
        totalModules: request.roadmapContext.totalModules,
        modulePosition: request.moduleContext.position,
        previousModules: request.moduleContext.prerequisites,
        nextModules: request.moduleContext.dependentModules
      }
    };
  }

  private applyContextualEnhancements(module: GeneratedModule, request: EnhancedModuleRequest): GeneratedModule {
    return {
      ...module,
      description: this.enhanceDescriptionWithContext(module.description || '', request),
      status: 'available' as const,
      completionPercentage: 0,
      lessons: module.lessons || []
    };
  }

  private enhanceDescriptionWithContext(description: string, request: EnhancedModuleRequest): string {
    const contextualInfo = `This module is part ${request.moduleContext.position} of ${request.roadmapContext.totalModules} in your learning journey.`;
    return `${description}\n\n${contextualInfo}`;
  }

  private alignObjectivesWithRoadmap(objectives: string[] | undefined, request: EnhancedModuleRequest): string[] {
    const roadmapObjectives = request.objectiveAlignment.roadmapObjectives;
    const moduleObjectives = objectives || [];
    return moduleObjectives.map(obj => {
      const alignedObjective = roadmapObjectives.find(ro => ro.toLowerCase().includes(obj.toLowerCase().split(' ')[0]));
      return alignedObjective ? `${obj} (aligned with: ${alignedObjective})` : obj;
    });
  }

  private enhanceContentWithContext(content: string, request: EnhancedModuleRequest): string {
    if (request.moduleContext.position > 1) {
      return `Building on previous modules, ${content}`;
    }
    return content;
  }

  private generateSkillProgression(request: EnhancedModuleRequest): SkillProgression[] {
    return request.skillContinuity.targetSkills.map((skill, index) => ({
      skillId: skill,
      skillName: skill.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      category: 'general',
      level: request.moduleContext.difficultyLevel,
      prerequisites: request.skillContinuity.previousSkills,
      dependentSkills: [],
      masteryIndicators: [`Master ${skill} concepts`],
      practiceActivities: [`Practice ${skill} exercises`]
    }));
  }

  private mapSkillDependencies(request: EnhancedModuleRequest): { [key: string]: string[] } {
    const dependencies: { [key: string]: string[] } = {};
    request.skillContinuity.targetSkills.forEach(skill => {
      dependencies[skill] = request.skillContinuity.previousSkills;
    });
    return dependencies;
  }

  private shouldAdaptContent(request: EnhancedModuleRequest): boolean {
    return request.roadmapContext.overallProgress > 25; // Adapt after 25% progress
  }

  private calculateProgressiveComplexity(request: EnhancedModuleRequest): boolean {
    return request.moduleContext.position > 1;
  }

  private verifyObjectiveAlignment(request: EnhancedModuleRequest): boolean {
    return request.objectiveAlignment.alignmentScore > 0.7;
  }

  private extractSkillProgression(modules: Module[], upToIndex: number): Array<{ skillId: string; currentLevel: number; targetLevel: number; masteryPercentage: number }> {
    const skills: Array<{ skillId: string; currentLevel: number; targetLevel: number; masteryPercentage: number }> = [];
    
    modules.slice(0, upToIndex + 1).forEach(module => {
      module.skillProgression?.forEach(sp => {
        skills.push({
          skillId: sp.skillId,
          currentLevel: sp.level,
          targetLevel: sp.level + 1,
          masteryPercentage: module.completionPercentage || 0
        });
      });
    });
    
    return skills;
  }

  private generateSkillContinuityMap(module: Module, allModules: Module[], index: number): { [key: string]: string[] } {
    const continuityMap: { [key: string]: string[] } = {};
    
    module.skillProgression?.forEach(skill => {
      const relatedSkills = allModules
        .slice(0, index)
        .flatMap(m => m.skillProgression || [])
        .filter(s => s.dependentSkills.includes(skill.skillId))
        .map(s => s.skillId);
      
      continuityMap[skill.skillId] = relatedSkills;
    });
    
    return continuityMap;
  }

  private generateAdaptiveQuestions(module: Module, userPreferences: UserPreferences): string[] {
    const baseQuestions = [
      'How confident do you feel about this topic?',
      'Which aspect would you like to focus on more?',
      'Do you need additional practice with any concept?'
    ];
    
    if (userPreferences.learningGoal === 'travel') {
      baseQuestions.push('How would you use this in a travel scenario?');
    }
    
    return baseQuestions;
  }

  private generateOverallSkillProgression(modules: Module[]): SkillProgression[] {
    const allSkills = modules.flatMap(m => m.skillProgression || []);
    const uniqueSkills = allSkills.filter((skill, index, self) => 
      index === self.findIndex(s => s.skillId === skill.skillId)
    );
    
    return uniqueSkills;
  }

  private createSkillDependencyMap(modules: Module[]): { [key: string]: string[] } {
    const dependencyMap: { [key: string]: string[] } = {};
    
    modules.forEach(module => {
      module.skillProgression?.forEach(skill => {
        dependencyMap[skill.skillId] = skill.prerequisites;
      });
    });
    
    return dependencyMap;
  }

  private generateProgressionMilestones(modules: Module[]): string[] {
    return modules.map((module, index) => 
      `Complete ${module.title} (Module ${index + 1})`
    );
  }
}

export const integrationBridgeService = new IntegrationBridgeService();
export type { 
  RoadmapContext, 
  ModuleContext, 
  IntegrationMetadata, 
  EnhancedModuleRequest, 
  EnhancedGeneratedModule 
};